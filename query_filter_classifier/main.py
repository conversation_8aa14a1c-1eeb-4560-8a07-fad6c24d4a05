import functions_framework
from typing import Dict, List, Any, Optional, Tuple
import base64
import time
import json
import re
import logging
import pandas as pd
from bson import ObjectId
from pymongo.collection import Collection
from const import (
    QUERY_COLLECTION_NAME, 
    QUERY_FILTER_CLASSIFIER_METADATA_KEY, 
    BASE_LOOPBACK_THRESHOLD,
    CHUNK_SIZE,
    QUERY_FILTER_COLLECTION_NAME,
    RAC_VOLUME_METADATA_KEY,
    RAC_COLLECTION_NAME_METADATA_KEY,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    PROJECT_ID,
    QUERY_FILTER_CLASSIFIER_TOPIC_ID
)
from utils.file_util import FileUtils
from utils.mongo_db import get_mongodb_db, get_mongodb_collection, get_mongodb_client
from utils.logger import logger
from utils.common_utils import (
    get_query_config, 
    update_query_metadata, 
    publish_pubsub_message
)
from utils.utilities import (
    DataSourceId,
    MetaContainer, 
    OrganizationAccountInfo,
    DiagnosticActionType, 
    DiagnosticStatus
)

def insert_classified_data_into_mongodb(
    classification_result: List[Dict] | Dict, 
    query_id: str, 
    mongodb_collection: Collection
) -> None:
    """
    Inserts classified data into MongoDB.
    
    Parameters:
    - classification_result (list or dict): The classified data to insert.
    - query_id (str): The ID associated with the query.
    - mongodb_collection (Collection): The MongoDB collection where data is inserted.
    
    Exception Handling:
    - Exception: Raised if an error occurs while inserting data into MongoDB.
    """
    try:
        if isinstance(classification_result, list):
            mongodb_collection.insert_many(classification_result)
        else:
            mongodb_collection.insert_one(classification_result)
        logger.info(f"Successfully inserted data for query_id {query_id}")
    except Exception as e:
        logger.error(f"Failed to insert data into MongoDB for query_id {query_id}: {e}")
        raise

def fetch_last_batch(query_id: str, mongodb_collection: Collection) -> List[Dict]:
    """
    Fetches all inserted records for the given query_id.
    
    Parameters:
    - query_id (str): The query ID to fetch previous batch records.
    - mongodb_collection (Collection): MongoDB collection to query.
    
    Returns:
    - List[Dict]: A list of dictionaries containing column classifications.
    """
    try:
        if not query_id:
            raise ValueError("query_id cannot be empty.")
        
        try:
            query_object_id = ObjectId(query_id)
        except Exception as obj_err:
            logger.error(f"Invalid query_id format: {query_id}")
            raise ValueError("Invalid query_id format. Expected a valid ObjectId.") from obj_err
        
        logger.info(f"Fetching last batch records for query_id: {query_id}")
        last_batches = list(mongodb_collection.find(
            {"query_id": query_object_id},
            {"_id": 1, "column": 1, "type": 1, "dataType": 1, "values": 1, "query_id": 1}
        ))
        
        if not last_batches:
            logger.warning(f"No records found for query_id: {query_id}")
            return []
        
        logger.info(f"Fetched {len(last_batches)} records for query_id: {query_id}")
        return last_batches  # List of dictionaries with stored classifications
    except Exception as e:
        logger.error(f"Unexpected error in fetch_last_batch for query_id {query_id}: {e}")
        return []

def update_classified_data_into_mongodb(
    mongodb_collection: Collection,
    record_id: Optional[str],
    query_id: str, 
    column: str, 
    datatype: str, 
    values: Optional[List] = None
) -> None:
    """
    Updates classified data into mongodb with new datatype and values only if needed.
    
    Parameters:
    - mongodb_collection: MongoDB collection to update.
    - query_id: The ID associated with the query.
    - column: Column name to update.
    - datatype: New datatype.
    - values: List of values (if applicable).
    """
    query = {"_id": ObjectId(record_id)} if record_id else {"query_id": ObjectId(query_id), "column": column} 
    update = {"$set": {"dataType": datatype}}
    
    if values is not None:
        update["$set"]["values"] = values  # Store values only if provided
    
    mongodb_collection.update_one(query, update, upsert=True)

def process_new_batch_with_last_batch(
    new_data_list: List[Dict], 
    old_data_list: List[Dict], 
    query_id: str, 
    mongodb_collection: Collection
) -> None:
    """
    Compares new data with old data using pandas DataFrames and updates MongoDB collection accordingly.
    
    Parameters:
    - new_data_list: List of new classification results.
    - old_data_list: List of previous classification results.
    - query_id: The ID associated with the query.
    - mongodb_collection: MongoDB collection to update.
    """
    if not new_data_list:
        logger.info("No new data to process")
        return
    
    # Convert to DataFrames
    df_new = pd.DataFrame(new_data_list)
    df_old = pd.DataFrame(old_data_list)
    
    # Check for required columns
    if 'column' not in df_new.columns:
        logger.error("New data missing 'column' field")
        return
    
    # Index by column for faster lookups
    if 'column' in df_old.columns and '_id' in df_old.columns:
        df_old.set_index('column', inplace=True)
    
    df_new_indexed = df_new.set_index('column')
    
    # Define datatype conversion rules
    conversion_rules = {
        ('number', 'text'): "text",
        ('date', 'text'): "text",
        ('list', 'text'): "text",
        ('number', 'date'): "text",
        ('number', 'list'): "text",
        ('list', 'number'): "text",
        ('list', 'date'): "text",
        ('empty', "date"): "date",
        ('empty', "text"): "text",
        ('empty', "number"): "text",
        ('empty', "longtext"): "longtext",
        ('number', "empty"): "number",
        ('text', "empty"): "text",
        ('date', "empty"): "date",
        ('longtext', "empty"): "longtext"

    }
    
    # Process each new record
    for column, new_row in df_new_indexed.iterrows():
        try:
            new_datatype = new_row.get("dataType")
            # new_values = new_row.get("values", [])
            
            # Skip invalid records
            if not column or new_datatype is None:
                logger.warning(f"Skipping invalid record with column: {column}")
                continue
                       
            old_record = df_old.loc[column] if column in df_old.index else None
            record_id = old_record["_id"] if old_record is not None else None
            old_datatype = old_record.get("dataType") if old_record is not None else None

            # Special case: text to anything except longtext
            if old_datatype == "text" and new_datatype != "longtext":
                logger.info(f"Preventing type change: Keeping column {column} as 'text'")
                update_classified_data_into_mongodb(mongodb_collection,record_id, query_id, column, "text", [])
                continue
            
            # Same type handling
            if old_datatype == new_datatype:
                if new_datatype == "list":
                    continue
            
            # Skip longtext as old type
            if old_datatype == "longtext":
                continue
                
            if new_datatype == "longtext":
                continue
            
            # Handle type conversions
            if (old_datatype, new_datatype) in conversion_rules:
                update_classified_data_into_mongodb(
                    mongodb_collection, 
                    record_id,
                    query_id, 
                    column,
                    conversion_rules[(old_datatype, new_datatype)], 
                    []
                )
                
        except Exception as e:
            logger.error(f"Error processing column {column}: {e}")

def classify_column(data: pd.DataFrame, query_id: str) -> List[Dict]:
    """
    Classifies columns in a given DataFrame based on data type and properties.
    
    Parameters:
    - data (pd.DataFrame): Input DataFrame containing the dataset.
    - query_id (str): Query ID in MongoDB.
    
    Returns:
    - List[Dict]: A list of dictionaries containing classification details for each column.
    """
    try:
        # Constants for classification
        MAX_UNIQUE_STR_THRESHOLD = 10
        MAX_CHARACTER_LENGTH_THRESHOLD = 50
        MAX_NUMERIC_ORDINAL_THRESHOLD = 20
        
        # Patterns for classification
        ordinal_marker_pattern = re.compile(r'(?:st|nd|rd|th)$', re.IGNORECASE)
        ordinal_word_pattern = re.compile(
            r'\b(?:first|second|third|fourth|fifth|sixth|seventh|eighth|ninth|tenth|eleventh|twelfth|'
            r'thirteenth|fourteenth|fifteenth|sixteenth|seventeenth|eighteenth|nineteenth|twentieth)\b',
            re.IGNORECASE
        )
        
        classified_columns = []
        
        for column in data.columns:
            try:
                column_values = data[column].copy()
                non_na_values = column_values.dropna()
                
                if non_na_values.empty:
                    logger.info(f"Column {column} is empty. Assigning type as 'empty'.")
                    classified_columns.append({
                        "column": column,
                        "type": "other",
                        "dataType": "empty",  # Marking empty column explicitly
                        "values": [],
                        "query_id": ObjectId(query_id)
                    })
                    continue  # Skip further classification logic for this column

                # Ensure non_na_values is 1D before calling unique()
                if isinstance(non_na_values, pd.DataFrame):
                    logger.warning(f"Unexpected DataFrame encountered in column {column}")
                    continue
                
                unique_vals = non_na_values.unique()
                num_unique = len(unique_vals)
                total_rows = len(column_values)
                
                logger.info(f"Processing column: {column} (Rows: {total_rows}, Unique Values: {num_unique})")
                
                classification = "other"
                column_type = "longtext"
                
                # Step 1: Detect Boolean Columns
                if set(non_na_values.unique()).issubset({0, 1, True, False}):
                    classification = "other"
                    column_type = "longtext"
                else:
                    # Step 2: Ensure 100% Purely Numeric Column
                    numeric_values = pd.to_numeric(non_na_values, errors='coerce')
                    num_numeric_values = numeric_values.notna().sum()
                    
                    if num_numeric_values == len(non_na_values):  # Must be 100% numeric
                        classification = "ordinal" if num_unique <= MAX_NUMERIC_ORDINAL_THRESHOLD else "cardinal"
                        column_type = "number"
                    else:
                        # Step 3: Ensure 100% Date Column
                        parsed_dates = pd.to_datetime(non_na_values, errors="coerce")
                        
                        if parsed_dates.notna().all():  # Must be 100% valid dates
                            classification = "ordinal"
                            column_type = "date"
                        else:
                            character_lengths = non_na_values.astype(str).str.len()
                            
                            if character_lengths.max() >= MAX_CHARACTER_LENGTH_THRESHOLD:
                                classification = "other"
                                column_type = "longtext"
                            else:
                                if num_unique <= MAX_UNIQUE_STR_THRESHOLD:
                                    has_ordinal_markers = pd.Series(non_na_values.astype(str)).str.contains(
                                        ordinal_marker_pattern, regex=True).any()
                                    has_ordinal_words = pd.Series(non_na_values.astype(str)).str.contains(
                                        ordinal_word_pattern, regex=True).any()
                                    
                                    classification = "ordinal" if has_ordinal_markers or has_ordinal_words else "cardinal"
                                    column_type = "text"
                                else:
                                    classification = "cardinal"
                                    column_type = "text"
                
                logger.debug(f"Column {column} classified as {classification} with type {column_type}")
                
                classified_columns.append({
                    "column": column,
                    "type": classification,
                    "dataType": column_type,
                    "values": [],
                    "query_id": ObjectId(query_id)
                })
                
            except Exception as column_error:
                logger.error(f"Error processing column '{column}' in query_id {query_id}: {column_error}")
        
        logger.info(f"Classification completed for query_id {query_id}")
        return classified_columns
    
    except Exception as e:
        logger.error(f"Error classifying columns for query_id {query_id}: {e}")
        raise

def filter_file(
    query:Dict[str, Any],
    file_utils: FileUtils,
    meta_container:Dict[str, Any],
    data_source_id,
    query_id: str,
    organization_db,
    hydration_bucket_name,
    payload: Dict[str, Any],
    query_collection

) -> Dict[str, Any]:
    """
    Filters and inserts filtered file data from the GCP bucket to MongoDB.
    
    Parameters:
    - query_id (str): Query ID in MongoDB.
    - hydration_bucket_name (str): GCP hydration bucket name.
    - mongodb_url (str): MongoDB connection URL.
    - organization_db_name (str): MongoDB database name.
    - file_utils (FileUtils): Utility functions for handling files.
    - payload (Dict[str, Any], optional): Additional payload information.
    
    Returns:
    - Dict[str, Any]: Status of the operation.
    """
    try:
        start_time = time.time()
        logger.info(f"Starting file filtering for query_id: {query_id}")
        
        if query is None:
            logger.error(f"Query not found for query_id: {query_id}")
            return {"status": 404, "message": "Query not found"}
        
        # Extract required metadata
        file_path = meta_container.meta_data["BUCKET_PATH"]
        if not file_path:
            logger.error("BUCKET_PATH is missing from metadata")
            return {"status": 400, "message": "Metadata missing BUCKET_PATH"}
        unifier_meta = meta_container.meta_data["UNIFIER_META"]
        data_source_name = query["source"]["data_source_name"]
        rac_collection_name = meta_container.meta_data[RAC_COLLECTION_NAME_METADATA_KEY]
        
        # Get necessary collections
        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)
        if rac_collection is None:
            logger.error(f"Failed to retrieve RAC collection: {rac_collection_name}")
            return {"status": 500, "message": "Database error: RAC collection not found"}
        classification_collection = get_mongodb_collection(organization_db, QUERY_FILTER_COLLECTION_NAME)
        
        # Get processing parameters
        offset = meta_container.meta_data.get(QUERY_FILTER_CLASSIFIER_METADATA_KEY, 0)
        total_documents = meta_container.meta_data.get(RAC_VOLUME_METADATA_KEY) or rac_collection.count_documents({}, hint="_id_")
    
        total_processed = 0
        logger.info(f"Processing file with total documents: {total_documents}, starting offset: {offset}")
 
        df = file_utils.parse_file_by_nrows(
            hydration_bucket_name,
            file_path,
            int(BASE_LOOPBACK_THRESHOLD),
            offset
        )
        
        # if df is None or df.empty:
        #     logger.info("No more data to process. Stopping loop.")
        #     return
        if df is None or df.empty:
            logger.info("No more data to process. Stopping loop.")
            return {"status": 204, "message": "No data to process"}
        

        batch_count = len(df)
        logger.info(f"Processing batch of {batch_count} records at offset {offset}")

        # Apply source-specific transformations
        # match data_source_id:
        #     case DataSourceId.FILE_UPLOAD.value: 
        #         unidata = list(unifier_meta.values())
        #         if unidata:
        #             df = df.drop(columns=unidata, errors="ignore")
        #     case DataSourceId.YT_COMMENTS.value:
        #         video_id_column = meta_container.meta_data.get("VIDEO_IDS_COLUMN")
        #         if video_id_column:
        #             logger.info(f"Dropping video_id column: {video_id_column}")
        #             df = df.drop(columns=[video_id_column],errors="ignore")
        #     case _:
        #         logger.warning(f"Unknown data source type: {data_source_name}")       
        
        if df.empty:
            logger.info("Empty dataframe encountered. Stopping processing.")
            return

        # Classify and process data
        classification_result = classify_column(df, query_id)
        if classification_result:
            if offset == 0:
                logger.info("Inserting first batch of classified data")
                insert_classified_data_into_mongodb(classification_result, query_id, classification_collection)
            else:
                logger.info("Processing subsequent batch of classified data")
                old_list = fetch_last_batch(query_id, classification_collection)
                process_new_batch_with_last_batch(classification_result, old_list, query_id, classification_collection)
            total_processed += batch_count
            logger.info(f"Total processed rows: {total_processed}")
        
        offset += batch_count
        # Check completion conditions
        match data_source_id:
            case DataSourceId.FILE_UPLOAD.value:
                complete_condition = int(offset) >= int(total_documents)
            case DataSourceId.YT_COMMENTS.value:
                complete_condition = int(offset) >= int(total_documents)
            case _:
                logger.warning(f"Unknown data source type: {data_source_name}")
        
        update_query_metadata(
            query_collection,
            query_id,
            QUERY_FILTER_CLASSIFIER_METADATA_KEY,
            offset,
        )

        if complete_condition:
            message = f"Capabilities file upload data query filter data classifier script completed successfully for query_id: {query_id}"
            logger.info(message)
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )
            logger.info(f"Completed processing all {total_documents} documents")
            
        else:
            print("Updating the offset")                
            if offset < total_documents:
                publish_pubsub_message(PROJECT_ID, QUERY_FILTER_CLASSIFIER_TOPIC_ID, payload)
                logger.info(
                    f"Published loopback message for offset {offset} out of {total_documents}"
                )

        # Summarize results
        end_time = time.time()
        processing_time = end_time - start_time
        logger.info(f"Total processing time: {processing_time:.2f} seconds")
        logger.info(f"Total processed rows: {total_processed}")
        
        return {
            "status": 200, 
            "message": "Successfully processed and inserted data.",
            "processed_rows": total_processed,
            "processing_time_seconds": round(processing_time, 2)
        }
    
    except Exception as e:
        error_message = f"An error occurred during file filtering: {str(e)}"
        logger.error(error_message, exc_info=True)
        return {"status": 400, "error": str(e)}
    
@functions_framework.cloud_event
def main(cloud_event):
    """
    Main function where execution starts after receiving a CloudEvent from a pubsub queue.
    
    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.
    
    Returns:
    - Dict: Status of the execution process.
    
    Raises:
    - Exception: If any error occurs during the execution process.
    """
    try:
        meta_container = MetaContainer()
        
        # Parse the payload from the cloud event
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info(f"Message received successfully: {payload}")
        
        # Set up metadata
        meta_container.set_payload_info(payload)
        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        
        # Get organization account information
        org_account_info = OrganizationAccountInfo(organization_id)
        hydration_bucket_name = org_account_info.hydration_bucket_name
        mongodb_url = org_account_info.mongodb_url

        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)

        meta_container.set_payload_info(payload)
        data_source_id = payload["data_source_id"]
   
        query_collection = get_mongodb_collection(organization_db, QUERY_COLLECTION_NAME)
        query = get_query_config(query_collection, query_id, {"_id": 0})
        
        meta_container.set_meta_data(query["meta_data"])
        
        # Create file utilities instance
        file_utils = FileUtils()
        
        # Execute the filter file function
        status = filter_file(
            query,
            file_utils,
            meta_container,
            data_source_id,
            query_id,
            organization_db,
            hydration_bucket_name,
            payload,
            query_collection
        )
        
        # Handle the result
        # if status is not None and status.get("status") == 200:
        #     logger.info(f"Query data classifier script completed successfully for query_id: {query_id}")
        #     message = f"Query data classifier completed successfully for query_id: {query_id}"
        # else:
        #     error_message = status.get("error", "Unknown error occurred in filter_file")
        #     logger.error(f"Filter file operation failed: {error_message}")
        if status is None or status.get("status") == 204:
            logger.info(f"No data to process for query_id: {query_id}")
            return  # Stop execution safely
        elif status.get("status") == 200:
            logger.info(f"Query data classifier script completed successfully for query_id: {query_id}")
        else:
            error_message = status.get("error", "Unknown error occurred in filter_file")
            logger.error(f"Filter file operation failed: {error_message}")    
    except Exception as e:
        message = f"An error occurred during capabilities file data classifier: {str(e)}"
        # Send diagnostic error message
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        
    finally:
        if mongodb_client is not None:
            mongodb_client.close()