# Cloud Build Configuration

# This Cloud Build configuration automates the deployment of Cloud Functions in Google Cloud.
# The process involves the following steps:

# 1. Set up logging to only log to Cloud Logging (CLOUD_LOGGING_ONLY).
# 2. Grant execute permission to the deploy script (deploy.sh).
# 3. Run the deploy.sh script to deploy the Cloud Function to Google Cloud.

# The Cloud Build service will execute this configuration on each build trigger to deploy the Cloud Function

options:
  logging: CLOUD_LOGGING_ONLY

steps:
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:latest'
  # - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        #!/bin/bash

        # Function to retrieve secrets from Secret Manager
        retrieve_secret() {
            gcloud secrets versions access latest --secret="$1"
        }

        _GCP_PROJECT=$(retrieve_secret "k8s-gcp-project")
        _REGION=$(retrieve_secret "k8s-region")
        _TAG=$(retrieve_secret "k8s-tag")
        _ACCESS_TOKEN=$(retrieve_secret "github-secret")
        _NAMESPACE=$(retrieve_secret "k8s-namespace")
        _CLUSTER_NAME=(retrieve_secret "k8s-cluster-name")

        # Function to authenticate with GKE if needed
        authenticate_gke() {
            if ! kubectl get nodes > /dev/null 2>&1; then
                gcloud container clusters get-credentials "${_CLUSTER_NAME}" --region "${_REGION}" --project "${_GCP_PROJECT}"
            else
                echo "Already authenticated to GKE cluster."
            fi
        }

        # Clone GitHub repo using secret token
        git config --global url."https://$<EMAIL>/".insteadOf "https://github.com/"
        git clone https://github.com/Tellagence-Capabilities/Capabilities.git
        # cd Capabilities || exit 1
        git fetch --unshallow

        # Define function folders for Kubernetes deployment
        declare -A folders=(
          ["vm-consumers"]="vm_consumers"
        )
        # Authenticate to GKE
        authenticate_gke
        
        # Detect utils folder change
        utils_changed=false
        if ! git diff --quiet HEAD~1 HEAD -- "utils"; then
            utils_changed=true
        fi

        for name in "${!folders[@]}"; do
          folder_changed=false
          if ! git diff --quiet HEAD~1 HEAD -- "${folders[$name]}" || [ "$utils_changed" = true ]; then
              folder_changed=true
          fi

          if [ "$folder_changed" = false ]; then
              echo "Skipping deployment for $name, no changes detected."
              touch /workspace/skip.txt
              exit 0
          fi
        done

        path="${folders[$name]}"

        cp -r "$(git rev-parse --show-toplevel)/utils" "$path"
        gcloud secrets versions access latest --secret="service-account" > "$path/key.json"
        gcloud secrets versions access latest --secret="${name}-k8s-env" > "$path/.env"
      

  - name: 'gcr.io/cloud-builders/docker'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ -f /workspace/skip.txt ]; then
          echo "Skipping Docker build for vm-consumers (no changes)."
          exit 0
        fi
        name=${_POD_NAME}
        path=${_PATH}
        project_id=${_GCP_PROJECT}
        image_name="gcr.io/${project_id}/${name}:latest"

        echo "Building and pushing Docker image for $name..."
        docker build -t "$image_name" "$path"
        docker push "$image_name"


  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:latest'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ -f /workspace/skip.txt ]; then
          echo "Skipping deployment for vm-consumers (no changes)."
          exit 0
        fi
        name=${_POD_NAME}
        namespace=${_NAMESPACE}
        if kubectl get pod "$name" -n "$namespace" > /dev/null 2>&1; then
          echo "Pod $name exists. Deleting it..."
          kubectl delete pod "$name" -n "$namespace" --ignore-not-found=true
          echo "Waiting for the pod to terminate..."
          sleep 30
        else
            echo "Pod $name does not exist. Creating a new pod..."
        fi

        gcloud secrets versions access latest --secret="pod-definition-yaml" > pod-definition.yaml

        echo "Applying YAML for $name..."
        kubectl apply -f pod-definition.yaml -n "$namespace"

        echo "Waiting for 30 seconds to allow the pod to start..."
        sleep 30