#!/bin/bash

: """
This script automates the deployment of Google Cloud Functions.

Workflow:
1. Retrieves secrets securely from Google Cloud Secret Manager.
2. Configures GitHub and fetches the latest code repository.
3. Defines Cloud Functions along with their respective configurations (memory, timeout, CPU, trigger type, etc.).
4. Checks for changes in function directories or the shared [ utils ] directory to determine necessary deployments.
5. Deploys Cloud Functions only if changes are detected, optimizing efficiency and reducing unnecessary deployments.

Lists of Cloud Functions and Configurations:
- [ function_folders ]: Maps Cloud Function names to their respective source directories.
- [ function_configs ]: Stores configuration settings such as memory allocation, timeout, CPU, triggers, and maximum instances for each Cloud Function.
"""

# Function to retrieve secrets from Secret Manager
retrieve_secret() {
    : """
    Retrieve the latest version of a secret from Google Cloud Secret Manager.
    
    This function securely fetches the latest version of a specified secret from 
    Google Cloud Secret Manager, ensuring sensitive credentials are not hardcoded.
    
    Args:
        $1 (string): The name of the secret to retrieve.
    
    Returns:
        string: The secret value retrieved from Secret Manager.
    """
    
    gcloud secrets versions access latest --secret="$1"
}

# Retrieve all required secrets
_ACCESS_TOKEN=$(retrieve_secret "github-secret")


# Configure GitHub access
git config --global url."https://$<EMAIL>/".insteadOf "https://github.com/"
git clone https://github.com/Tellagence-Capabilities/Capabilities.git
git fetch --unshallow

# List of functions to check for changes
declare -A function_folders=(
    ["append"]="append"
    ["sequence-coordinator"]="sequence_coordinator"
    ["data-cleansing"]="common_cleansing"
    ["encapsulation"]="encapsulation"
    ["data-retention"]="data_retention"
    ["data-transformer"]="data_transformer"
    ["diagnostic"]="diagnostic_engine"
    ["file-upload-data-unifier"]="file_upload/data_unifier"
    ["file-upload-hydration"]="file_upload/hydration"
    ["notification"]="notification"
    ["query-archiver-retriever"]="query_archiver"
    ["query-data-export"]="query_data_export"
    ["query-filter-classifier"]="query_filter_classifier"
    ["query-migrator"]="query_migrator"
    ["query-optimizer"]="query_optimizer"
    ["query-throttler"]="query_throttler"
    ["recovery-engine"]="recovery_engine"
    ["sentiment"]="sentiment"
    ["sentiment-multi"]="sentiment_multi"
    ["yt-comments-data-unifier"]="youtube_comments/data_unifier"
    ["yt-comments-hydration"]="youtube_comments/hydration"
)

# Define a configuration for each function including max-instances and CPU
declare -A function_configs=(
    ["append"]="--trigger-topic=append"
    ["sequence-coordinator"]="--trigger-topic=capabilities-sequence-coordinator"
    ["data-cleansing"]="--trigger-topic=data-cleansing"
    ["encapsulation"]="--trigger-topic=data-encapsulation"
    ["data-retention"]="--trigger-topic=data-retention"
    ["data-transformer"]="--trigger-topic=data-transformer"
    ["diagnostic"]="--trigger-topic=diagnostic"
    ["file-upload-data-unifier"]="--trigger-topic=file-upload-data-unifier"
    ["file-upload-hydration"]="--trigger-topic=file-upload-hydration"
    ["notification"]="--trigger-topic=notification"
    ["query-archiver-retriever"]=" --trigger-topic=query-archiver-retriever"
    ["query-data-export"]="--trigger-topic=query-data-export"
    ["query-filter-classifier"]="--trigger-topic=query-filter-classifier"
    ["query-migrator"]="--trigger-topic=query-migrator"
    ["query-optimizer"]="--trigger-topic=query-optimizer"
    ["query-throttler"]="--trigger-topic=query-throttler"
    ["recovery-engine"]="--trigger-http"
    ["sentiment"]="--trigger-topic=sentiment"
    ["sentiment-multi"]="--trigger-topic=sentiment-multi"
    ["yt-comments-data-unifier"]="--trigger-topic=yt-comments-api-data-unifier"
    ["yt-comments-hydration"]="--trigger-topic=yt-comments-api-hydration"
)

retrieve_env_file() {
    : """
    Retrieve environment variables from Google Cloud Secret Manager.
    
    Args:
        $1 (string): The name of the function whose environment variables are being retrieved.
    
    Returns:
        None. Writes environment variables to a temporary file.
    """
    local function_name=$1
    local env_file="${function_name}-env"
    gcloud secrets versions access latest --secret="$env_file" > "/tmp/${function_name}.env"
}

# Function to deploy Cloud Function
deploy_function() {
    : """
    Deploy a Google Cloud Function with the specified configuration and settings.
    
    This function is responsible for deploying an individual Cloud Function based 
    on its function name, source directory, configuration settings, and environment variables.
    
    Args:
        $1 (string): Name of the Cloud Function to deploy.
        $2 (string): Path to the function's source code.
        $3 (string): Configuration options such as memory, timeout, CPU, and triggers.

    Returns:
        None. The function is deployed using Google Cloud's CLI.
    """

    local name=$1
    local path=$2
    local config=$3

    retrieve_env_file "$name"
    
    # Deploy the Cloud Function if changes are detected
    echo "Deploying '$name' cloud function..."
    cd "$path"
    
    # Copying utils to Cloud Function
    cp -r "$(git rev-parse --show-toplevel)/utils" .

    source "/tmp/${name}.env"
    _ENV_VARS=$(grep -v '^#' "/tmp/${name}.env" | tr '\n' ',' | sed 's/,$//')

    if [ "$name" == "query-data-export" ]; then
        gcloud secrets versions access latest --secret="service-account" > service-account.json
        mv service-account.json utils/
    fi


    # Deploy the Cloud Function with the provided configurations
    gcloud functions deploy "$name" \
        --runtime python312 \
        --entry-point main \
        $config \
        --region us-central1 \
        --source . \
        --set-env-vars "$_ENV_VARS" \
        --no-allow-unauthenticated
    cd -
}

utils_changed=false
if ! git diff --quiet HEAD~1 HEAD -- "utils"; then
    utils_changed=true
fi

pids=()

for function_name in "${!function_folders[@]}"; do

    folder_changed=false
    if ! git diff --quiet HEAD~1 HEAD -- "${function_folders[$function_name]}" || [ "$utils_changed" = true ]; then
        folder_changed=true
    fi

    # Deploy if changes were detected
    if [ "$folder_changed" = true ]; then
        config="${function_configs[$function_name]}"
        deploy_function "$function_name" "${function_folders[$function_name]}" "$config" &
        pids+=($!)  # Save the background process ID
    else
        echo "Skipping deployment for $function_name, no changes detected."
    fi
done

# Wait for all deployments to finish
for pid in "${pids[@]}"; do
    wait "$pid"
done
