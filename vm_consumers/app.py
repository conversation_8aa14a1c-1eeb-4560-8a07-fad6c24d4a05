import os
from flask import Flask, request, jsonify

from dotenv import load_dotenv

load_dotenv()

from utils.logger import logger
from utils.utilities import PublisherType, VmConsumers
from consumer_scripts.Summarization.trigger import trigger_summarization
from consumer_scripts.Similarity.similarity import trigger_similarity
from consumer_scripts.Discover.discover import trigger_discover
from consumer_scripts.Categorization_tool.categorization_tool import (
    trigger_categorization,
)
import threading


app = Flask(__name__)


def run_task(consumer_name, payload):
    try:
        logger.info("Starting the consumer %s", consumer_name)

        match consumer_name:
            case VmConsumers.SIMILARITY_VM.value:
                logger.info("Running Similarity VM consumer")
                trigger_similarity(payload)

            case VmConsumers.SUMMARIZATION_VM.value:
                logger.info("Running Summarization VM consumer")
                trigger_summarization(payload)

            case VmConsumers.DISCOVER.value:
                logger.info("Running Discover consumer")
                trigger_discover(payload)

            case VmConsumers.CATEGORIZATION_TOOL.value:
                logger.info("Running Categorization Tool consumer")
                trigger_categorization(payload)

            case _:
                logger.info("Script not found for %s", consumer_name)

    except Exception as e:
        logger.error("Error on running the consumer %s: %s", consumer_name, e)


@app.route("/run/<consumer_name>", methods=["POST"])
def run_script(consumer_name):
    payload = request.json
    logger.info("Received payload: %s", payload)

    # Start the long-running task in a new thread
    thread = threading.Thread(target=run_task, args=(consumer_name, payload))
    thread.start()

    logger.info("Execution started for consumer: %s", consumer_name)
    return jsonify(
        {
            "message": f"{consumer_name} execution started successfully",
        }
    )


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080)