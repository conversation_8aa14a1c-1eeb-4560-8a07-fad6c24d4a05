import os

LOOPBACK_THRESHOLD_BATCH_COUNT = os.getenv("LOOPBACK_THRESHOLD_BATCH_COUNT")
PROJECT_ID = os.getenv("PROJECT_ID")
CLEANSING_TOPIC_ID = os.getenv("CLEANSING_TOPIC_ID")
YT_DATA_UNIFIER_TOPIC_ID = os.getenv("YT_DATA_UNIFIER_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
CONSUMER_TYPE = os.getenv("CONSUMER_TYPE")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")

REPLIES = "replies"
COMMENTS = "comments"
