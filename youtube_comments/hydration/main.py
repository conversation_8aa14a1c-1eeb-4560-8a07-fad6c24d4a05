import base64
from bson import ObjectId
import json
from pymongo import UpdateOne
from const import (
    COMMENTS,
    INVALID_VIDEO_IDS_MSG_TEMPLATE,
    PROJECTION,
    QUERY_COLLECTION_NAME,
    PROJECT_ID,
    DATA_UNIFIER_TOPIC_ID,
    HYDRATION_TOPIC_ID,
    BASE_LOOPBACK_THRESHOLD,
    RAC_COLLECTION_NAME_METADATA_KEY,
    INVALID_VIDEO_IDS_METADATA_KEY,
)
import functions_framework
import pandas as pd
import numpy as np
from cloudevents.http import CloudEvent
from googleapiclient.discovery import build
from google.cloud import storage
from API_methods import (
    create_batch_data,
    fetch_video_info,
    retrieve_replies,
    retrieve_comments,
    handle_quota_exceeded
)
from utils.file_util import FileUtils
from utils.mongo_db import get_mongodb_db, get_mongodb_collection, get_mongodb_client
from utils.common_utils import (
    get_query_config,
    generate_collection_name,
    update_query_metadata,
    update_volume,
)
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
    MongoDBCollection
)

storage_client = storage.Client()
meta_container = MetaContainer()


def sum_total_reply_count(df):
    """
    Calculate the sum of 'totalReplyCount' values in the 'snippet' column of the DataFrame.

    - This function extracts the 'totalReplyCount' value from each dictionary in the 'snippet' column,
    substitutes 0 for any missing 'totalReplyCount' keys, and returns the total sum of these values.

    Parameters:
    - df (pd.DataFrame): A pandas DataFrame containing a 'snippet' column with dictionaries.

    Returns:
    - int: The sum of 'totalReplyCount' values from all dictionaries in the 'snippet' column.

    Exception Handling:
    - None
    """
    return df["snippet"].apply(lambda snippet: snippet.get("totalReplyCount", 0)).sum()


def update_loopback_checkpoint(
    payload, row_index, video_id, page, current_page_token, comments_per_video_id
):
    """
    Ensure 'hydration_loopback_checkpoint' exists in the payload dictionary
    and update its values with the provided parameters.

    Parameters:
    - payload (dict): Pub sub message payload.
    - row_index (int): The current row index to set in 'hydration_loopback_checkpoint'.
    - video_id (str): The current video id to set in 'hydration_loopback_checkpoint'.
    - page (int): The current comment page number to set in 'hydration_loopback_checkpoint'.
    - current_page_token (str): The current comment page token to set in 'hydration_loopback_checkpoint'.
    - comments_per_video_id (int): The number of comments per video Id.

    Returns:
    - None

    Exception Handling:
    - None
    """
    payload.setdefault("hydration_loopback_checkpoint", {}).update(
        {
            "current_row_index": row_index,
            "current_video_id": video_id,
            "current_comment_page": page,
            "current_comment_page_token": current_page_token,
            "comments_per_video_id": comments_per_video_id,
        }
    )


def fetch_comments_replies(
    df,
    youtube_api,
    key_column,
    batch_collection,
    query_collection,
    collection,
    query_id,
    payload,
    total_record_count=0,
    current_comment_page_token=None,
    current_comment_page=1,
    comments_per_video_id=0,
):
    """
    Fetches youtube comments and replies by iterating over list of video ids

    Args:
    - df (DataFrame) : Dataframe of the file with video ids
    - youtube (googleapiclient.build) : youtube API build object
    - batch_collection : MongoDB Collection object for the batches
    - query_collection (pymongo.collection.Collection): The MongoDB query collection.
    - collection (dump): Collection to dump the data
    - query_id (str) : query_id for the query
    - payload (dict): Pub sub message payload.

    Returns:
    - is_loopback_required (bool): Indicates whether loopback is required.

    Raises:
    - Exception: If unable to fetch youtube comments and replies.
    """
    try:
        loopback_threshold = total_record_count + int(BASE_LOOPBACK_THRESHOLD)
        # Looping through video_ids dataframe starting from the current_row_index to fetch comments data for each video ID
        for _, row in df.iterrows():
            user_upload_info = row.replace({np.nan: None}).to_dict()
            # Video ID of the video you're interested in
            video_id = row[key_column]
            logger.info("Fetching comments for video_id: '%s'", video_id)

            # Fetch video information for a particular video_id
            video_info = fetch_video_info(youtube_api, meta_container, video_id)

            # Determine the current page token, Initial page and comments per video Id to start processing
            current_page_token = current_comment_page_token
            page = current_comment_page

            while True:
                # Fetch comments for the video
                comments_response = retrieve_comments(
                    youtube_api, video_id, meta_container, current_page_token
                )
                # Update invalid video ids in metadata
                if comments_response is None:
                    query_collection.update_one(
                        {"_id": ObjectId(query_id)},
                        {
                            "$addToSet": {
                                f"meta_data.{INVALID_VIDEO_IDS_METADATA_KEY}": video_id
                            }
                        },
                    )
                    break

                next_page_token = comments_response.get("nextPageToken")
                if (
                    next_page_token is not None
                    and next_page_token == current_page_token
                ):
                    message = (
                        f"Next page token matches the current page token ({next_page_token}) "
                        f"while fetching comments for video ID {video_id}."
                    )
                    raise Exception(message)

                batches = []
                comments = comments_response["items"]
                if comments:
                    comments_df = pd.DataFrame(comments)
                    comments_record_count = len(comments_df)
                    comments_df["user_upload_info"] = [
                        user_upload_info
                    ] * comments_record_count

                    # Add video information in comments
                    if video_info:
                        comments_df["video_info"] = [video_info] * comments_record_count

                    # Calculate the sum of 'totalReplyCount' for a specific comment response page
                    total_reply_count_sum = sum_total_reply_count(comments_df)
                    if total_reply_count_sum >= int(BASE_LOOPBACK_THRESHOLD):
                        raise Exception(
                            f"Total reply count sum ({total_reply_count_sum}) for comment page {page} of video ID {video_id} exceeds the loopback threshold."
                        )

                    if (
                        total_record_count
                        + comments_record_count
                        + total_reply_count_sum
                    ) >= loopback_threshold:
                        return True

                    total_record_count += comments_record_count
                    payload["total_record_count"] = total_record_count
                    create_batch_data(
                        comments_response,
                        video_id,
                        page,
                        COMMENTS,
                        query_id,
                        current_page_token,
                        next_page_token,
                        batches=batches,
                    )

                    # Mark each comments with record_type as comment
                    comments_df["record_type"] = "comment"
                    # collection.insert_many(
                    #     comments_df.replace({np.nan: None}).to_dict(orient="records")
                    # )
                    bulk_series = comments_df.apply(
                        lambda x: UpdateOne(
                            {"id": x["id"]},
                            {"$set": x.replace({np.nan: None}).to_dict()},
                            upsert=True,
                        ),
                        axis=1,
                    )
                    update_records = bulk_series.to_list()
                    collection.bulk_write(update_records)
                else:
                    message = "Comments are empty even though there was page cursor"
                    logger.warning(message)
                    logger.info(f"comments:{comments} video_id:{video_id}")
                    if page == 1:
                        query_collection.update_one(
                            {"_id": ObjectId(query_id)},
                            {
                                "$addToSet": {
                                    f"meta_data.{INVALID_VIDEO_IDS_METADATA_KEY}": video_id
                                }
                            },
                        )
                        break

                # iterate over comments and replies
                for comment in comments:
                    # check if reply count is greater than one
                    total_reply_count = comment["snippet"]["totalReplyCount"]
                    if total_reply_count:
                        replies = retrieve_replies(
                            youtube_api,
                            comment["id"],
                            video_id,
                            query_id,
                            batches,
                        )
                        if replies:
                            replies_df = pd.DataFrame(replies)
                            replies_record_count = len(replies_df)
                            replies_df["user_upload_info"] = [
                                user_upload_info
                            ] * replies_record_count
                            # Add video information in replies
                            if video_info:
                                replies_df["video_info"] = [
                                    video_info
                                ] * replies_record_count
                            total_record_count += replies_record_count
                            # Mark each reply with record_type as reply
                            replies_df["record_type"] = "reply"
                            bulk_series = replies_df.apply(
                                lambda x: UpdateOne(
                                    {"id": x["id"]}, {"$set": x.to_dict()}, upsert=True
                                ),
                                axis=1,
                            )
                            update_records = bulk_series.to_list()
                            collection.bulk_write(update_records)
                        else:
                            message = (
                                "Empty replies even though the totalReplyCount is not 0"
                            )
                            logger.warning(message)
                            logger.info(f"replies:{replies}")
                            logger.info(f"totalReplyCount : {total_reply_count}")

                if batches:
                    batch_collection.insert_many(batches)
                payload["total_record_count"] = total_record_count
                # Update the current page token and increment page number
                current_page_token = next_page_token
                page += 1
                # If no more pages, break the loop
                if not next_page_token:
                    break
                if total_record_count >= loopback_threshold:
                    return True

            logger.info(
                "Successfully fetched and inserted all %d comments and replies for video ID: '%s'",
                comments_per_video_id,
                video_id,
            )
            batch_collection.update_many(
                {"query_id": ObjectId(query_id), "video_id": video_id},
                {"$set": {"hydration_status": "COMPLETED"}},
            )

        logger.info(
            "Successfully fetched and inserted all %d comments and replies for query ID: '%s'",
            total_record_count,
            query_id,
        )
        return False

    except Exception as e:
        message = f"Error while fetching youtube comments and replies: {e}"
        logger.exception(message)
        error_details = e.error_details[0] if e.error_details else {}
        handle_quota_exceeded(video_id, meta_container, error_details)
        raise


def handle_invalid_video_ids(query_collection, query_id, query_name):
    """
    Checks for invalid video IDs in the metadata of a specified query and sends a diagnostic message for it.

    Parameters:
    - query_collection (pymongo.collection.Collection): The MongoDB query collection.
    - query_id (str): The unique identifier of the query document in the collection.
    - query_name (str): Name of the query.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if there is an error during handling invalid video IDs.
    """
    try:
        # Fetch the invalid video IDs from MongoDB
        document = query_collection.find_one(
            {"_id": ObjectId(query_id)},
            {f"meta_data.{INVALID_VIDEO_IDS_METADATA_KEY}": 1, "_id": 0},
        )
        # Extract the invalid video IDs
        invalid_video_ids = document.get("meta_data", {}).get(
            INVALID_VIDEO_IDS_METADATA_KEY, []
        )
        # Send diagnostic
        if invalid_video_ids:
            formatted_ids = ", ".join(map(str, invalid_video_ids))
            message = INVALID_VIDEO_IDS_MSG_TEMPLATE.format(
                formatted_ids=formatted_ids, query_name=query_name, query_id=query_id
            )
            meta_container.send_diagnostic(
                DiagnosticActionType.INFO.value,
                DiagnosticStatus.INFO.value,
                message,
            )
    except Exception as e:
        message = f"Error while handling invalid video IDs for query_id {query_id}: {e}"
        logger.error(message)
        raise


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    -----------
    Description
    -----------
    This is the main function were the execution start
    Steps:
        - Parse the payload and get the query_id
        - Set the DB connection and query collection
        - Get query details from MongoDB
        - set the meta data fro the query if query is not None
        - Get the API KEY , batch collection name and file path
        - Read the file with videos ids from GCP
        - Fetch comments and replies according with given video ids
        - If the total record count is greater than or equal to the loopback threshold:
            - Publish a message to the hydration consumer for loopback for the remaining records.
        - Else:
            - Publish message to youtube comments data unifier
    ----------
    Parameters
    -----------
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    ----------
    Returns
    ----------
        - Json object based on the success or failure
          - Success : {"status": 200}
          - Failure : {"status": 400, "message": <error_message>}

    """
    mongodb_client = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        query_id = payload["query_id"]
        organization_id = payload["organization_id"]
        loopback_checkpoint = payload.get("hydration_loopback_checkpoint", {})

        org_account_info = OrganizationAccountInfo(organization_id)
        hydration_bucket_name = org_account_info.hydration_bucket_name
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name
        rac_collection_suffix = org_account_info.rac_collection_suffix

        # set the mongo database connection and get query details
        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )
        query = get_query_config(query_collection, query_id, PROJECTION)

        if query is None:
            logger.warning(f"No query config for query_id {query_id}")

        data_source_meta_data = {
            "data_source_name": query["source"]["data_source_name"],
        }

        meta_container.set_query_collection(query_collection)
        meta_container.set_meta_data(data_source_meta_data)
        if not loopback_checkpoint:
            message = f"Capabilities youtube comments hydration script started successfully for query_id: {query_id}"
            meta_container.send_diagnostic(
                DiagnosticActionType.INSERT.value,
                DiagnosticStatus.INACTIVE.value,
                message,
            )

        meta_container.update_meta_data(query["meta_data"])

        batch_collection_name = meta_container.meta_data.get("BATCH_COLLECTION_NAME")
        rac_collection_name = meta_container.meta_data.get("RAC_COLLECTION_NAME")
        file_path = meta_container.meta_data.get("BUCKET_PATH")
        query_name = query["name"]

        file_util = FileUtils()
        file_bytes = file_util.download_file(hydration_bucket_name, file_path)
        file_extension = file_util.check_file_extension(file_path)
        df = file_util.parse_file(file_bytes, file_extension)

        if rac_collection_name:
            logger.info(
                "RAC collection name has already been updated in the query metadata, for query_id: '%s'",
                query_id,
            )
        else:
            # set the collection name and get the collection objects
            rac_collection_name = generate_collection_name(
                query_id, query_name, rac_collection_suffix
            )
            update_query_metadata(
                query_collection,
                query_id,
                RAC_COLLECTION_NAME_METADATA_KEY,
                rac_collection_name,
            )

        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)
        rac_collection.create_index([("id", 1)], unique=True)
        batch_collection = get_mongodb_collection(
            organization_db, batch_collection_name
        )

        # set the build for youtube API and fetch comments and replies
        key_column = meta_container.meta_data["VIDEO_IDS_COLUMN"]
        df = df.groupby(key_column).first().reset_index()
        # Set up the YouTube API service and Video IDs files
        user_api_key = organization_db["user_credentials"].find_one(
            {"user_id": payload["user_id"]}
        )
        org_api_key = (
            (
                organization_db[MongoDBCollection.ORGANIZATION_DATA_SOURCE.value].find_one(
                    {"data_source_id": ObjectId(payload.get("data_source_id"))},
                    {"meta_data.ORG_API_KEY": 1},
                )
                or {}
            )
            .get("meta_data", {})
            .get("ORG_API_KEY")
        )
        api_key = user_api_key or org_api_key
        youtube_api_build = build("youtube", "v3", developerKey=api_key)

        videos = list(
            batch_collection.aggregate(
                [
                    {
                        "$match": {
                            "query_id": ObjectId(query_id),
                            "batch_type": "comments",
                        }
                    },
                    {"$sort": {"video_id": 1, "page_no": -1}},
                    {
                        "$group": {
                            "_id": {"video_id": "$video_id"},
                            "batch_id": {"$first": "$_id"},
                            "comment_ids": {"$first": "$ids"},
                            "total_count": {"$sum": {"$size": "$ids"}},
                            "page_no": {"$first": "$page_no"},
                            "hydration_status": {"$first": "$hydration_status"},
                            "latest_page_cursor": {"$first": "$current_page_cursor"},
                            "latest_next_cursor": {"$first": "$next_page_cursor"},
                        }
                    },
                    {
                        "$project": {
                            "video_id": "$_id.video_id",
                            "batch_id": 1,
                            "comment_ids": 1,
                            "total_count": 1,
                            "page_no": 1,
                            "hydration_status": 1,
                            "latest_page_cursor": 1,
                            "latest_next_cursor": 1,
                        }
                    },
                ]
            )
        )

        function_params = {
            "df": df,
            "youtube_api": youtube_api_build,
            "key_column": key_column,
            "batch_collection": batch_collection,
            "query_collection": query_collection,
            "collection": rac_collection,
            "query_id": query_id,
            "payload": payload,
        }

        ids_to_ignore = meta_container.meta_data.get(INVALID_VIDEO_IDS_METADATA_KEY, [])
        if videos:
            videos_df = pd.DataFrame(videos)
            ids_to_ignore = (
                videos_df[videos_df["hydration_status"] == "COMPLETED"][
                    "video_id"
                ].to_list()
                + ids_to_ignore
            )
            pending_df = videos_df.loc[videos_df["hydration_status"] != "COMPLETED"]
            function_params["total_record_count"] = int(videos_df["total_count"].sum())

            if not pending_df.empty:
                function_params["comments_per_video_id"] = int(
                    pending_df["total_count"].iloc[0]
                )
                function_params["current_comment_page"] = (
                    int(pending_df["page_no"].iloc[0]) + 1
                )
                # function_params["current_comment_page"] = int(pending_df['page_no'].iloc[0])
                function_params["current_comment_page_token"] = str(
                    pending_df["latest_next_cursor"].iloc[0]
                )

        incomplete_df = df.loc[~df[key_column].isin(ids_to_ignore)]
        incomplete_df.reset_index(drop=True, inplace=True)
        function_params["df"] = incomplete_df

        is_loopback_required = fetch_comments_replies(**function_params)
        if is_loopback_required:
            message = f"Capabilities youtube comments hydration script looping back for query_id {query_id}"
            publish_pubsub_message(PROJECT_ID, HYDRATION_TOPIC_ID, payload)
        else:
            handle_invalid_video_ids(query_collection, query_id, query_name)
            update_volume(rac_collection, query_collection, query["_id"])
            publish_pubsub_message(PROJECT_ID, DATA_UNIFIER_TOPIC_ID, payload)
            message = f"Capabilities youtube comments hydration script completed successfully for query_id {query_id}"
            logger.info(message)
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )

        return {"status": 200, "message": message}, 200

    except Exception as e:
        message = (
            f"An error occurred during capabilities youtube comments hydration: {e}"
        )
        logger.exception(message)
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            e,
        )
        return {"status": 400, "message": message}, 400
    finally:
        if mongodb_client is not None:
            mongodb_client.close()
