"""
Module for managing organization setup, user creation, MongoDB and Google
Cloud Storage integration, and quota management for a platform.

This module provides a series of functions for handling the following tasks:
- Setting up and configuring MongoDB Atlas and Google Cloud Storage resources for an organization.
- Creating and managing MongoDB database users with specific roles and permissions.
- Inserting and updating organization-related data and credentials in the vault.
- Managing data sources and BigQuery schemas.
- Inserting user and organization credentials into respective collections.
- Managing organization quotas limits.
"""

from typing import Optional, Dict
from datetime import datetime, timezone
import time
from copy import deepcopy
from urllib.parse import quote_plus
import string
import secrets
from bson import ObjectId
import requests
from pymongo.database import Database
from pymongo.collection import Collection
from pymongo import MongoClient
from requests.auth import HTTPDigestAuth
from parameters import *
from google.cloud import storage

from const import (
    ATLAS_API_ENDPOINT,
    ATLAS_API_PRIVATE_KEY,
    ATLAS_API_PUBLIC_KEY,
    ATLAS_PROJECT_ID,
    CLIENT_API_MONGODB_URL,
    DATA_EXPORT_BUCKET_PREFIX,
    HYDRATION_BUCKET_PREFIX,
    HYDRATION_TEMP_BUCKET_PREFIX,
    IS_CLIENT_API_ENABLED_META_DATA_KEY,
    IS_CATEGORIZATION_ENABLED_META_DATA_KEY,
    ORG_USERS_COLLECTION,
    SHOW_ANALYSIS_META_DATA_KEY,
    ORG_MONGO_URI_HOSTNAME,
    ORGANIZATION_VAULT_COLLECTION,
    ORGANIZATION_DOMAIN_INFO_COLLECTION,
    QUERY_ARCHIVE_BUCKET_PREFIX,
    MODEL_NAME,
    MONGODB_URI_TEMPLATE,
    ORG_CREDS_COLLECTION,
    ORG_QUOTA_CONFIG_COLLECTION,
    PROJECT_ID,
    PLATFORM_MONGODB_URL,
    ORGANIZATION_MONGODB_URL,
    CAPABILITIES_DATABASE,
    SERVICE_ACCOUNT_EMAIL,
    USER_CREDS_COLLECTION,
    USER_INFO_COLLECTION,
    DATA_SOURCE_OBJECT_ID,
    YOUTUBE_BIG_QUERY_SCHEMA,
    FILE_UPLOAD_BIG_QUERY_SCHEMA,
    BIG_QUERY_SCHEMA_META_DATA_KEY,
    IS_BIG_QUERY_ENABLED_META_DATA_KEY,
    RESPONSE_TIMEOUT,
)
from utils.logger import logger
from utils.utilities import MongoDBCollection


def create_db_user(
    username: str,
    password: str,
    db_name: str,
    organization_exists: bool,
    role: Optional[str] = "readWrite",
) -> str:
    """
    Creates a MongoDB Atlas database user with specified access.

    Parameters:
    - username (str): Username for the new database user
    - password (str): Password for the new database user
    - db_name (str): Name of the database the user will have access to
    - organization_exists (bool) : boolean if the organization already exists
    - role (str, optional): Role for the user on the specified database (default is "readWrite")

    Returns:
    - str: MongoDB connection string if the user is created successfully, otherwise None

    Exception Handling:
    - None
    """
    if not organization_exists and USE_INTERNAL_MONGODB:
        # Define the API endpoint URL for creating a database user
        url = ATLAS_API_ENDPOINT.format(project_id=ATLAS_PROJECT_ID)

        # Define headers
        headers = {
            "Accept": "application/vnd.atlas.2023-01-01+json",
            "Content-Type": "application/vnd.atlas.2023-01-01+json",
        }

        # Define the payload with user details
        user_data = {
            "databaseName": "admin",  # Use "admin" to specify database authentication context
            "roles": [{"databaseName": db_name, "roleName": role}],
            "username": username,
            "password": password,
        }

        # Send POST request to create the user
        response = requests.post(
            url,
            auth=HTTPDigestAuth(ATLAS_API_PUBLIC_KEY, ATLAS_API_PRIVATE_KEY),
            headers=headers,
            json=user_data,
            timeout=RESPONSE_TIMEOUT,
        )

        # Check the response status
        if response.status_code == 201:
            logger.info("User created successfully: %s", response.json())
            time.sleep(30)
            return MONGODB_URI_TEMPLATE.format(
                username=username,
                password=quote_plus(password),
                org_mongo_uri_hostname=ORG_MONGO_URI_HOSTNAME,
            )

        logger.error("Failed to create user: %s", response.json())


def get_organization_details(platform_db: Database) -> tuple:
    """This function give the organization details

    Parameters:
      platform_db (Database): platform database object

    Returns:
      - organization_exists (bool) : boolean if the organization already exists
      - organization_id (ObjectId) : ObjectId of the newly created org or existing org
    """
    organization_collection = platform_db["organization_domain_information"]
    organization = organization_collection.find_one({"name": ORGANIZATION_NAME})
    organization_exists = False
    organization_id = None

    if organization is not None:
        organization_exists = True
        organization_id = organization["_id"]

    if not organization_exists:
        result = organization_collection.insert_one(
            {"name": ORGANIZATION_NAME, "domain": ORGANIZATION_DOMAIN}
        )
        organization_id = result.inserted_id

        logger.info(
            "Successfully inserted organization '%s' with domain '%s' (organization ID: %s).",
            ORGANIZATION_NAME,
            ORGANIZATION_DOMAIN,
            organization_id,
        )

    return organization_exists, organization_id


def create_bucket(
    project_id: str,
    is_organization_exists: bool,
    organization_id: ObjectId,
    bucket_prefix: str,
) -> str:
    """
    Creates a Google Cloud Storage bucket for a new organization if it does not already exist.

    Parameters:
    - project_id (str): The ID of the Google Cloud project where the bucket will be created.
    - is_organization_exists (bool): A flag indicating whether the organization already exists.
    - organization_id (ObjectId): The unique ID of the organization, used to name the bucket.
    - bucket_prefix (str): The prefix for the bucket name, followed by the organization ID.

    Returns:
    - str: The name of the created bucket.

    Exception Handling:
    - None
    """
    # Create a Cloud Storage client
    client = storage.Client(project=project_id)

    # Specify the name for your new bucket
    bucket_name = f"{bucket_prefix}_{organization_id}"

    if not is_organization_exists:
        # Create the bucket (if not already created)
        bucket = client.bucket(bucket_name)
        bucket = client.create_bucket(bucket, location="US")

        logger.info("Bucket %s created.", bucket.name)

        # Get the current IAM policy
        policy = bucket.get_iam_policy(requested_policy_version=3)

        # Grant the service account editor access to the bucket
        policy.bindings.append(
            {
                "role": "roles/storage.objectAdmin",  # Editor role for objects in the bucket
                "members": {f"serviceAccount:{SERVICE_ACCOUNT_EMAIL}"},
            }
        )

        # Set the updated IAM policy
        bucket.set_iam_policy(policy)

        logger.info(
            "Granted roles/storage.objectAdmin role to %s on bucket %s.",
            SERVICE_ACCOUNT_EMAIL,
            bucket_name,
        )
    return bucket_name


# Function to generate a secure password
def generate_secure_password(length: int = 16) -> str:
    """Function to generate a secure password"""
    characters = string.ascii_letters + string.digits + string.punctuation
    return "".join(secrets.choice(characters) for i in range(length))


# Vault credentials for the organization
def insert_org_vault_info(
    mongo_client: MongoClient,
    organization_exists: bool,
    organization_id: ObjectId,
    org_db_name: str,
    client_api_mongodb_url: str,
    organization_mongodb_url: str,
    hydration_bucket_name: str,
    hydration_temp_bucket_name: str,
    query_archive_bucket_name: str,
    data_export_bucket_name: str,
) -> tuple[str, str]:
    """
    Inserts the credentials for an organization in the organization vault.

    Parameters:
    - mongo_client (MongoClient): The MongoDB client instance to interact with the database.
    - organization_exists (bool): A flag indicating if the organization already exists.
    - organization_id (ObjectId): The unique ID of the organization.
    - org_db_name (str): The name of the organization's database.
    - client_api_mongodb_url (str): The MongoDB connection URL for the client API.
    - organization_mongodb_url (str): The MongoDB connection URL for the organization.
    - hydration_bucket_name (str): The name of the bucket used for data hydration.
    - hydration_temp_bucket_name (str): The name of the temporary bucket used for data hydration.
    - query_archive_bucket_name (str): The name of the bucket used for archiving query data.
    - data_export_bucket_name (str): The name of the bucket used for exporting query data .

    Returns:
    - tuple: A tuple containing the MongoDB URL (`organization_mongodb_url`) and the database
        name (`org_db_name`).

    Exception Handling:
    - None
    """
    organization_vault_cred = {
        "organization_id": organization_id,
        "organization_name": ORGANIZATION_NAME,
        "organization_db_name": org_db_name,
        "hydration_bucket_name": hydration_bucket_name,
        "hydration_temp_bucket_name": hydration_temp_bucket_name,
        "query_archive_bucket_name": query_archive_bucket_name,
        "data_export_bucket_name": data_export_bucket_name,
        "rac_collection_suffix": "rac",
        "client_api_mongodb_url": client_api_mongodb_url,
        "mongodb_url": organization_mongodb_url,
    }

    organization_vault_db = mongo_client[ORGANIZATION_VAULT_COLLECTION]
    organization_collection = f"{organization_id}_account_information"
    organization_account_information = organization_vault_db[organization_collection]

    if not organization_exists:
        organization_account_information.insert_one(organization_vault_cred)
        logger.info(
            "Inserted credentials in organization vault for organization ID: %s",
            organization_id,
        )

    else:
        vault_cred = organization_account_information.find_one({})
        organization_mongodb_url = vault_cred["mongodb_url"]

    return organization_mongodb_url, org_db_name


def get_big_query_schema(data_source_name: str) -> Dict:
    """
    Retrieves the predefined BigQuery schema based on the specified data source name.

    Parameters:
    - data_source_name (str): The name of the data source for which the schema is required.

    Returns:
    - dict: The corresponding predefined BigQuery schema for the given data source name.

    Exception Handling:
    - None
    """
    match data_source_name:
        case "YouTube Comments":
            return YOUTUBE_BIG_QUERY_SCHEMA
        case "File Upload":
            return FILE_UPLOAD_BIG_QUERY_SCHEMA
        case _:
            logger.warning(
                "Invalid data source name: %s. Default schema (File Upload) is being used."
            )
            return FILE_UPLOAD_BIG_QUERY_SCHEMA


def insert_data_sources(
    data_source_object_id: Dict,
    organization_exists: bool,
    organization_id: ObjectId,
    organization_data_source_collection: Collection,
) -> None:
    """
    Inserts data source records for an organization into the organization's
    data source collection.

    Parameters:
    - data_source_object_id (dict): A dictionary mapping data source names to
        their respective object IDs.
    - organization_exists (bool): A flag indicating whether the organization
        already exists.
    - organization_id (ObjectId): The unique ID of the organization.
    - organization_data_source_collection (Collection): The MongoDB collection
        to insert data sources into.

    Returns:
    - None

    Exception Handling:
    - None
    """
    meta_data = {
        "IS_BATCH_ENABLED": False,
    }
    if CLIENT_SPECIFIED_CATEGORIES and IS_CATEGORIZATION_ENABLED:
        meta_data["CLIENT_SPECIFIED_CATEGORIES"] = CLIENT_SPECIFIED_CATEGORIES

    data_source_record = {
        "meta_data": meta_data,
        "organization_id": organization_id,
    }

    if not organization_exists:
        data_sources = []

        for data_source in DATASOURCES:
            # We can update data source id according to data source selected
            big_query_schema = get_big_query_schema(data_source)
            data_source_copy = deepcopy(data_source_record)
            data_source_copy["data_source_name"] = data_source
            data_source_copy["data_source_id"] = data_source_object_id[data_source]
            data_source_copy["meta_data"][
                BIG_QUERY_SCHEMA_META_DATA_KEY
            ] = big_query_schema
            data_sources.append(data_source_copy)

        organization_data_source_collection.insert_many(data_sources)

        logger.info(
            "Enabled %s data sources for organization Id: %s",
            ", ".join(DATASOURCES),
            organization_id,
        )


def insert_user(capabilities_db: Database) -> ObjectId:
    """
    Inserts a user into the user collection of the specified database if not already present.

    Parameters:
    - capabilities_db (Database): The MongoDB database object.

    Returns:
    - ObjectId: The ID of the inserted or existing user.

    Exception Handling:
    - None
    """
    user_collection = capabilities_db[USER_INFO_COLLECTION]

    user = user_collection.find_one({"email": USER_EMAIL})
    if user:
        return user["_id"]

    user = {
        "email": USER_EMAIL,
        "password": None,
        "first_name": FIRST_NAME,
        "last_name": LAST_NAME,
        "tokens": [],
        "refresh_tokens": [],
        "new_user_status": True,
        "last_login": None,
        "image_url": None,
        "created_at": datetime.now(timezone.utc),
        "username": f"{FIRST_NAME} {LAST_NAME}",
    }

    user_result = user_collection.insert_one(user)
    logger.info("New user '%s' inserted successfully.", user["username"])
    return user_result.inserted_id


def configure_user_org_settings(
    capabilities_db: Database,
    user_id: ObjectId,
    organization_id: ObjectId,
    role: str,
    job_title: str,
    query_limit: int,
    data_point_limit: int,
    notification_preferences: Dict,
) -> None:
    """
    Configures organization-specific settings for a user.

    This function ensures that a user-organization mapping exists by inserting a new record or
    updating an existing one. It sets the following settings:
    - User role within the organization
    - Job title
    - Query limit
    - Data point limit
    - Notification preferences (e.g., email, sound, in-app notifications)

    Parameters:
    - capabilities_db (Database): The MongoDB database object.
    - user_id (ObjectId): The ID of the user.
    - organization_id (ObjectId): The ID of the organization.
    - role (str): The role of the user in the organization.
    - job_title (str): User's job title in the organization.
    - query_limit (int): Maximum queries allowed.
    - data_point_limit (int): Maximum data points allowed.
    - notification_preferences (dict): Notification settings for the user in the organization.

    Exception Handling:
    - None
    """
    org_users_collection = capabilities_db[ORG_USERS_COLLECTION]

    org_users_collection.update_one(
        {"user_id": user_id, "organization_id": organization_id},
        {
            "$set": {
                "user_id": user_id,
                "organization_id": organization_id,
                "role": role,
                "job_title": job_title,
                "query_limit": query_limit,
                "data_point_limit": data_point_limit,
                "notification_preferences": notification_preferences,
            },
        },
        upsert=True,
    )

    logger.info(
        "Configured organization settings for user '%s' in organization '%s' with role '%s', job title '%s', "
        "query limit %d, data point limit %d, and notification preferences: %s.",
        user_id,
        organization_id,
        role,
        job_title,
        query_limit,
        data_point_limit,
        notification_preferences,
    )


# Credentials insertion
def insert_credentials(
    data_source_object_id: Dict, organization_db, user_id: Database
) -> ObjectId:
    """
    Inserts or updates organization and user credentials in the respective collections.

    Parameters:
    - data_source_object_id (dict): A dictionary mapping data source names to their respective IDs.
    - organization_db (Database): The MongoDB database object for the organization's data.
    - user_id (ObjectId): The unique identifier of the user to associate with the user credentials.

    Returns:
    - None

    Exception Handling:
    - None
    """
    org_credentials_collection = organization_db[ORG_CREDS_COLLECTION]
    user_credentials_collection = organization_db[USER_CREDS_COLLECTION]

    # Insert organization credentials such as Generative AI API keys
    for consumer_type, api_key in GEN_AI_CREDS.items():
        if api_key:
            org_cred = {
                "API_KEY": api_key,
                "CONSUMER_TYPE": consumer_type,
                "MODEL_NAME": MODEL_NAME,
            }
            org_credentials_collection.update_one(
                org_cred,
                {"$set": org_cred},
                upsert=True,
            )
            logger.info(
                "Successfully updated organization credentials for '%s' consumer_type",
                consumer_type,
            )

    # Insert user credentials such as YouTube API keys
    if YOUTUBE_API_KEY:
        user_cred = {
            "name": CRED_NAME,
            "meta_data": {"API_KEY": YOUTUBE_API_KEY},
            "user_id": user_id,
            "data_source_id": data_source_object_id["YouTube Comments"],
        }
        user_credentials_collection.update_one(
            user_cred, {"$set": user_cred}, upsert=True
        )
        logger.info("Successfully updated user credentials.")


def update_org_quota(
    org_quota_config_collection: Collection, organization_id: ObjectId
) -> None:
    """
    Updates the organization's quota configuration for queries and data points.

    Parameters:
    - org_quota_config_collection (pymongo.collection.Collection): MongoDB
        collection object for organization quota config.
    - organization_id (ObjectId): The ID of the organization.

    Returns:
    - None

    Exception Handling:
    - None
    """

    # Define the filter for the organization quota document
    org_quota_config_filter = {
        "organization_id": organization_id,
    }

    # Define the quota configuration
    org_quota_config = {
        "query_limit": ORG_QUERY_LIMIT,
        "data_point_limit": ORG_DATA_POINT_LIMIT,
    }

    # Update or insert the quota configuration
    org_quota_config_collection.update_one(
        org_quota_config_filter, {"$set": org_quota_config}, upsert=True
    )

    logger.info(
        "Successfully Updated organization quota: org_id=%s"
        "query_limit=%s, data_point_limit=%s",
        organization_id,
        ORG_QUERY_LIMIT,
        ORG_DATA_POINT_LIMIT,
    )


def update_organization_metadata(
    organization_domain_info_collection: Collection, organization_id: str
) -> None:
    """
    Updates the meta_data field of an organization document in the MongoDB collection.

    Parameters:
    - organization_domain_info_collection (pymongo.collection.Collection):
        The MongoDB collection containing organization domain information.
    - organization_id (str):
        The ID of the organization to update.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Define the update operation
    update_operation = {
        "$set": {
            f"meta_data.{IS_BIG_QUERY_ENABLED_META_DATA_KEY}": IS_BIG_QUERY_ENABLED,
            f"meta_data.{IS_CLIENT_API_ENABLED_META_DATA_KEY}": IS_CLIENT_API_ENABLED,
            f"meta_data.{IS_CATEGORIZATION_ENABLED_META_DATA_KEY}": IS_CATEGORIZATION_ENABLED,
            f"meta_data.{SHOW_ANALYSIS_META_DATA_KEY}": SHOW_ANALYSIS,
        }
    }
    # Perform the update
    result = organization_domain_info_collection.update_one(
        {"_id": ObjectId(organization_id)}, update_operation
    )

    if result.matched_count == 0:
        logger.warning("No organization found with ID: %s", organization_id)
    if result.modified_count > 0:
        logger.info(
            "Successfully updated meta_data for organization ID: %s", organization_id
        )


def main():
    """This is main method where execution start and perform the following steps
        - Setup the connection with platform mongodb
        - Check if the organization exists
        - Create hydration, hydration temp, query archive, query data export buckets if organization doesn't exists and get the bucket name
        - Create a MongoDB Atlas database user with specified access
        - Insert the credentials in organization vault and get organization mongodb url
            organization DB name
        - Connect to organization database
        - Get the organization data source and insert data sources for organization
        - Insert user and get the user_id.
        - Updates the configuration settings for a specific user within an organization, such as role, job title, query limit, data point limit, and notification preferences.
        - Inserts or updates organization and user credentials in the respective collections
        - Updates the organization's quota configuration.
        - Update the organization's metadata information.

    Parameters:
    - None

    Returns:
    - None

    Exception Handling:
    - None
    """
    mongo_client = MongoClient(PLATFORM_MONGODB_URL)
    capabilities_db = mongo_client[CAPABILITIES_DATABASE]

    organization_exists, organization_id = get_organization_details(capabilities_db)
    organization_domain_info_collection = capabilities_db[
        ORGANIZATION_DOMAIN_INFO_COLLECTION
    ]

    # Create hydration bucket for the organization
    hydration_bucket_name = create_bucket(
        PROJECT_ID, organization_exists, organization_id, HYDRATION_BUCKET_PREFIX
    )

    # Create hydration temp bucket for the organization
    hydration_temp_bucket_name = create_bucket(
        PROJECT_ID, organization_exists, organization_id, HYDRATION_TEMP_BUCKET_PREFIX
    )

    # Create query archive bucket for the organization
    query_archive_bucket_name = create_bucket(
        PROJECT_ID, organization_exists, organization_id, QUERY_ARCHIVE_BUCKET_PREFIX
    )
    # Create data export bucket for the organization
    data_export_bucket_name = create_bucket(
        PROJECT_ID, organization_exists, organization_id, DATA_EXPORT_BUCKET_PREFIX
    )
    # Create a MongoDB Atlas database user with specified access
    org_db_name = ORGANIZATION_NAME + "_" + str(organization_id) + "_" + "org"
    org_db_user_name = ORGANIZATION_NAME + "_" + str(organization_id) + "_" + "user"

    # Generate a secure password for organization database user
    org_db_user_password = generate_secure_password()
    organization_mongodb_url = create_db_user(
        org_db_user_name, org_db_user_password, org_db_name, organization_exists
    )
    organization_mongodb_url = organization_mongodb_url or ORGANIZATION_MONGODB_URL

    # Insert credentials in the organization vault
    organization_mongodb_url, organization_db_name = insert_org_vault_info(
        mongo_client,
        organization_exists,
        organization_id,
        org_db_name,
        CLIENT_API_MONGODB_URL,
        organization_mongodb_url,
        hydration_bucket_name,
        hydration_temp_bucket_name,
        query_archive_bucket_name,
        data_export_bucket_name,
    )

    # Organization database and organization datasource creation
    organization_mongo_client = MongoClient(
        organization_mongodb_url.replace("-pri", "")
    )
    organization_db = organization_mongo_client[organization_db_name]
    organization_data_source_collection = organization_db[MongoDBCollection.ORGANIZATION_DATA_SOURCE.value]
    # Get organization quota config collection
    org_quota_config_collection = capabilities_db[ORG_QUOTA_CONFIG_COLLECTION]

    # Establish connections to client API's MongoDB
    client_api_org_mongo_client = MongoClient(
        CLIENT_API_MONGODB_URL.replace("-pri", "")
    )
    client_api_org_db = client_api_org_mongo_client[organization_db_name]
    client_api_org_data_source_collection = client_api_org_db[
        MongoDBCollection.ORGANIZATION_DATA_SOURCE.value 
    ]

    insert_data_sources(
        DATA_SOURCE_OBJECT_ID,
        organization_exists,
        organization_id,
        organization_data_source_collection,
    )

    # If the client API is enabled, insert data sources into the client API's MongoDB
    if IS_CLIENT_API_ENABLED:
        insert_data_sources(
            DATA_SOURCE_OBJECT_ID,
            organization_exists,
            organization_id,
            client_api_org_data_source_collection,
        )

    user_id = insert_user(capabilities_db)

    # Updates the configuration settings for a specific user within an organization, such as role, job title, query limit, data point limit, and notification preferences.
    configure_user_org_settings(
        capabilities_db,
        user_id,
        organization_id,
        USER_ROLE,
        JOB_TITLE,
        USER_QUERY_LIMIT,
        USER_DATA_POINT_LIMIT,
        NOTIFICATION_PREFERENCES,
    )

    insert_credentials(DATA_SOURCE_OBJECT_ID, organization_db, user_id)

    # Updates the organization's quota configuration
    update_org_quota(org_quota_config_collection, organization_id)

    # Update the organization's metadata information
    update_organization_metadata(organization_domain_info_collection, organization_id)


main()
