"""
This module contains constants used in files of Onboarding consumer.
"""

from enum import Enum
import os
from bson import ObjectId
from utils.utilities import RacTransform, DataSourceId

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = ""  # path to service account

RESPONSE_TIMEOUT = 300  # 5 mins

ATLAS_API_ENDPOINT = os.getenv("ATLAS_API_ENDPOINT")
ATLAS_API_PUBLIC_KEY = os.getenv("ATLAS_API_PUBLIC_KEY")
ATLAS_API_PRIVATE_KEY = os.getenv("ATLAS_API_PRIVATE_KEY")
ATLAS_PROJECT_ID = os.getenv("ATLAS_PROJECT_ID")

CLIENT_API_MONGODB_URL = os.getenv("CLIENT_API_MONGODB_URL")
PLATFORM_MONGODB_URL = os.getenv("PLATFORM_MONGODB_URL")
ORGANIZATION_MONGODB_URL = os.getenv("ORGANIZATION_MONGODB_URL")

PROJECT_ID = os.getenv("PROJECT_ID")
CAPABILITIES_DATABASE = os.getenv("CAPABILITIES_DATABASE")
ORG_CREDS_COLLECTION = os.getenv("ORG_CREDS_COLLECTION")
ORG_MONGO_URI_HOSTNAME = os.getenv("ORG_MONGO_URI_HOSTNAME")
ORGANIZATION_DOMAIN_INFO_COLLECTION = os.getenv("ORGANIZATION_DOMAIN_INFO_COLLECTION")
ORG_QUOTA_CONFIG_COLLECTION = os.getenv("ORG_QUOTA_CONFIG_COLLECTION")
ORGANIZATION_VAULT_COLLECTION = os.getenv("ORGANIZATION_VAULT_COLLECTION")
SERVICE_ACCOUNT_EMAIL = os.getenv("SERVICE_ACCOUNT_EMAIL")
USER_CREDS_COLLECTION = os.getenv("USER_CREDS_COLLECTION")
USER_INFO_COLLECTION = os.getenv("USER_INFO_COLLECTION")
ORG_USERS_COLLECTION = os.getenv("ORG_USERS_COLLECTION")

DATA_SOURCE_OBJECT_ID = {
    "YouTube Comments": ObjectId(DataSourceId.YT_COMMENTS.value),
    "File Upload": ObjectId(DataSourceId.FILE_UPLOAD.value),
    "Database": ObjectId(DataSourceId.DATABASE_AS_DATASOURCE.value),
    "Sprinklr API": ObjectId(DataSourceId.SPRINKLR_API.value),
}
HYDRATION_BUCKET_PREFIX = "hydration_bucket"
HYDRATION_TEMP_BUCKET_PREFIX = "hydration_bucket_temp"
QUERY_ARCHIVE_BUCKET_PREFIX = "query_archive_bucket"
DATA_EXPORT_BUCKET_PREFIX = "data_export_bucket"
MODEL_NAME = [
    "gemini-1.5-flash",
    "gemini-2.0-flash-lite",
    "gemini-2.0-flash",
    "gemini-2.5-flash",
    "gemini-2.5-flash-lite",
    "gemini-2.5-pro",
]
MONGODB_URI_TEMPLATE = "mongodb+srv://{username}:{password}@{org_mongo_uri_hostname}"
BIG_QUERY_SCHEMA_META_DATA_KEY = "BIG_QUERY_SCHEMA"
IS_BIG_QUERY_ENABLED_META_DATA_KEY = "IS_BIG_QUERY_ENABLED"
IS_CLIENT_API_ENABLED_META_DATA_KEY = "IS_CLIENT_API_ENABLED"
IS_CATEGORIZATION_ENABLED_META_DATA_KEY = "IS_CATEGORIZATION_ENABLED"
SHOW_ANALYSIS_META_DATA_KEY = "SHOW_ANALYSIS"

YOUTUBE_BIG_QUERY_SCHEMA = {
    RacTransform._id.value: "STRING",
    RacTransform.Tellagence_ID.value: "STRING",
    RacTransform.Tellagence_Date.value: ["TIMESTAMP", "DATETIME"],
    RacTransform.Tellagence_Text.value: "STRING",
    RacTransform.YT_etag.value: "STRING",
    RacTransform.YT_kind.value: "STRING",
    RacTransform.YT_record_type.value: "STRING",
    RacTransform.YT_replies.value: "STRING",
    RacTransform.YT_author.value: "STRING",
    RacTransform.YT_author_channel_url.value: "STRING",
    RacTransform.YT_author_profile_image_url.value: "STRING",
    RacTransform.YT_comment_id.value: "STRING",
    RacTransform.YT_like_count.value: "STRING",
    RacTransform.YT_reply_count.value: "STRING",
    RacTransform.YT_video_id.value: "STRING",
    RacTransform.BODY1.value: "STRING",
    RacTransform.EMOJIS.value: "STRING",
    RacTransform.EMOJIS_Unique.value: "STRING",
    RacTransform.EMOJIS_Unique_Count.value: "INTEGER",
    RacTransform.Hashtag.value: "STRING",
    RacTransform.Hashtag_Position.value: "FLOAT",
    RacTransform.Hashtag_Unique.value: "STRING",
    RacTransform.Hashtag_Unique_Count.value: "INTEGER",
    RacTransform.Keyword.value: "STRING",
    RacTransform.Lemitized.value: "STRING",
    RacTransform.Phrase.value: "STRING",
    RacTransform.encapsulation_marker.value: "STRING",
    RacTransform.cluster.value: "INTEGER",
    RacTransform.Stories.value: "INTEGER",
    RacTransform.Themes.value: "INTEGER",
    RacTransform.Unique_Cluster_ID.value: "INTEGER",
    RacTransform.Unique_Story_ID.value: "INTEGER",
    RacTransform.cluster_summary.value: "STRING",
    RacTransform.story_summary.value: "STRING",
    RacTransform.theme_summary.value: "STRING",
    RacTransform.cluster_sentiment.value: "STRING",
    RacTransform.cluster_sentiment_reasoning.value: "STRING",
    RacTransform.snippet_channelId.value: "STRING",
    RacTransform.snippet_videoId.value: "STRING",
    RacTransform.snippet_canReply.value: "BOOLEAN",
    RacTransform.snippet_totalReplyCount.value: "INTEGER",
    RacTransform.snippet_isPublic.value: "BOOLEAN",
    RacTransform.snippet_topLevelComment_kind.value: "STRING",
    RacTransform.snippet_topLevelComment_etag.value: "STRING",
    RacTransform.snippet_topLevelComment_id.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_channelId.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_videoId.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_textDisplay.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_textOriginal.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorDisplayName.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorProfileImageUrl.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorChannelUrl.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorChannelId_value.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_canRate.value: "BOOLEAN",
    RacTransform.snippet_topLevelComment_snippet_viewerRating.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_likeCount.value: "INTEGER",
    RacTransform.snippet_topLevelComment_snippet_publishedAt.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_updatedAt.value: "STRING",
    RacTransform.snippet_textDisplay.value: "STRING",
    RacTransform.snippet_textOriginal.value: "STRING",
    RacTransform.snippet_parentId.value: "STRING",
    RacTransform.snippet_authorDisplayName.value: "STRING",
    RacTransform.snippet_authorProfileImageUrl.value: "STRING",
    RacTransform.snippet_authorChannelUrl.value: "STRING",
    RacTransform.snippet_canRate.value: "BOOLEAN",
    RacTransform.snippet_viewerRating.value: "STRING",
    RacTransform.snippet_likeCount.value: "STRING",
    RacTransform.snippet_publishedAt.value: "STRING",
    RacTransform.snippet_updatedAt.value: "STRING",
    RacTransform.snippet_authorChannelId_value.value: "STRING",
    RacTransform.video_info_snippet_publishedAt.value: "STRING",
    RacTransform.video_info_snippet_channelId.value: "STRING",
    RacTransform.video_info_snippet_title.value: "STRING",
    RacTransform.video_info_snippet_description.value: "STRING",
    RacTransform.video_info_snippet_thumbnails_default_url.value: "STRING",
    RacTransform.video_info_snippet_thumbnails_default_width.value: "STRING",
    RacTransform.video_info_snippet_thumbnails_default_height.value: "STRING",
    RacTransform.video_info_snippet_channelTitle.value: "STRING",
    RacTransform.video_info_snippet_tags.value: "STRING",
    RacTransform.video_info_snippet_categoryId.value: "STRING",
    RacTransform.video_info_snippet_liveBroadcastContent.value: "STRING",
    RacTransform.video_info_snippet_defaultLanguage.value: "STRING",
    RacTransform.video_info_snippet_defaultAudioLanguage.value: "STRING",
    RacTransform.video_info_contentDetails_duration.value: "STRING",
    RacTransform.video_info_contentDetails_dimension.value: "STRING",
    RacTransform.video_info_contentDetails_definition.value: "STRING",
    RacTransform.video_info_contentDetails_caption.value: "STRING",
    RacTransform.video_info_statistics_viewCount.value: "INTEGER",
    RacTransform.video_info_statistics_likeCount.value: "INTEGER",
    RacTransform.video_info_statistics_favoriteCount.value: "INTEGER",
    RacTransform.video_info_statistics_commentCount.value: "INTEGER",
    f"YT_{RacTransform.date_utc_str.value}": "STRING",
    f"YT_{RacTransform.date_utc.value}": "DATE",
    RacTransform.video_info_contentDetails_contentRating.value: "STRING",
    RacTransform.video_info_contentDetails_regionRestriction_blocked.value: "STRING",
    RacTransform.video_info_contentDetails_licensedContent.value: "BOOLEAN",
}


FILE_UPLOAD_BIG_QUERY_SCHEMA = {
    RacTransform._id.value: "STRING",
    RacTransform.Tellagence_ID.value: "STRING",
    RacTransform.Tellagence_Date.value: ["TIMESTAMP", "DATETIME"],
    RacTransform.Tellagence_Text.value: "STRING",
    RacTransform.BODY1.value: "STRING",
    RacTransform.EMOJIS.value: "STRING",
    RacTransform.EMOJIS_Unique.value: "STRING",
    RacTransform.EMOJIS_Unique_Count.value: "INTEGER",
    RacTransform.Hashtag.value: "STRING",
    RacTransform.Hashtag_Position.value: "FLOAT",
    RacTransform.Hashtag_Unique.value: "STRING",
    RacTransform.Hashtag_Unique_Count.value: "INTEGER",
    RacTransform.Keyword.value: "STRING",
    RacTransform.Lemitized.value: "STRING",
    RacTransform.Phrase.value: "STRING",
    RacTransform.encapsulation_marker.value: "STRING",
    RacTransform.cluster.value: "INTEGER",
    RacTransform.Stories.value: "INTEGER",
    RacTransform.Themes.value: "INTEGER",
    RacTransform.Unique_Cluster_ID.value: "INTEGER",
    RacTransform.Unique_Story_ID.value: "INTEGER",
    RacTransform.cluster_summary.value: "STRING",
    RacTransform.story_summary.value: "STRING",
    RacTransform.theme_summary.value: "STRING",
    RacTransform.cluster_sentiment.value: "STRING",
    RacTransform.cluster_sentiment_reasoning.value: "STRING",
    f"FL_{RacTransform.date_utc_str.value}": "STRING",
    f"FL_{RacTransform.date_utc.value}": "DATE",
}


class UserRole(Enum):
    """Enumeration for user role."""

    ADMIN = "admin"
    STANDARD = "standard"
    ARCHIVED = "archived"


class JobTitle(Enum):
    """Enumeration for job title."""

    DEVELOPER = "Developer"
    ANALYST = "Analyst"
    QA = "QA"
