"""
This module contains Mandatory parameters used in Onboarding consumer.
"""

from const import JobTit<PERSON>, UserRole

# onboarding organization parameters

ORGANIZATION_NAME = ""
ORGANIZATION_DOMAIN = ""
ORG_DATA_POINT_LIMIT = -1  # No limit (-1 indicates unlimited, update as needed)
ORG_QUERY_LIMIT = -1  # No limit (-1 indicates unlimited, update as needed)

USER_DATA_POINT_LIMIT = 5000000  # Update as needed
USER_QUERY_LIMIT = 10000  # Update as needed

# Onboarding user parameters
USER_EMAIL = ""
FIRST_NAME = ""
LAST_NAME = ""

# Needs to be created while onboarding a user
YOUTUBE_API_KEY = ""
CRED_NAME = FIRST_NAME + "_" + LAST_NAME + "_YouTube_cred"

DATASOURCES = ["YouTube Comments", "File Upload"]

# Generative AI API keys: Add API keys while onboarding new organization for each consumer type
GEN_AI_CREDS = {
    "SENTIMENT": "",
    "SENTIMENT_MULTI": "",
    "SUMMARIZATION": "",
    "CATEGORIZATION_TOOL": "",
}

# OPTIONAL/DEFUALT PARAMETERS
USER_ROLE = (
    UserRole.STANDARD.value
)  # Default user role is standard, can be changed to admin or archived as needed

JOB_TITLE = (
    JobTitle.ANALYST.value
)  # Default job title is analyst, can be changed to other titles as needed


# Specifies if the organization's database should be hosted within our system's MongoDB instance (True) or externally (False).
USE_INTERNAL_MONGODB = True

# Indicates whether the BigQuery is enabled for the organization (True for enabled, False for disabled)
IS_BIG_QUERY_ENABLED = False

# Indicates whether the client API is enabled for the organization (True for enabled, False for disabled)
IS_CLIENT_API_ENABLED = False

IS_CATEGORIZATION_ENABLED = True

SHOW_ANALYSIS = True

# By default notification preferences are enabled, change to False if needed
NOTIFICATION_PREFERENCES = {
    "enabled": True,
    "query_completion": {"email": True, "notification": True, "sound": True},
    "query_interruption": {"email": True, "notification": True, "sound": True},
    "shared_query": {"email": True, "notification": True, "sound": True},
    "shared_project": {"email": True, "notification": True, "sound": True},
}

# Client specified categories for categorization tool, update as needed
CLIENT_SPECIFIED_CATEGORIES = []
