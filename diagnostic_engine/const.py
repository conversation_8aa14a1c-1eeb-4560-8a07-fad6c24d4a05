import os

from utils.utilities import PublisherType

PROJECT_ID = os.getenv("PROJECT_ID")
NOTIFICATION_TOPIC_ID = os.getenv("NOTIFICATION_TOPIC_ID")

CAPABILITIES_MONGO_URI = os.getenv("CAPABILITIES_MONGO_URI")
CAPABILITIES_DB_NAME = os.getenv("CAPABILITIES_DB_NAME")
CAPABILITIES_DIAGNOSTIC_COLLECTION = os.getenv("CAPABILITIES_DIAGNOSTIC_COLLECTION")
CAPABILITIES_CONSUMER_LIST_COLLECTION = os.getenv(
    "CAPABILITIES_CONSUMER_LIST_COLLECTION"
)
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
WAIT_TIME = os.getenv("WAIT_TIME")

ANALYSIS_STATUS_METADATA_KEY = "ANALYSIS_STATUS"
BIGQUERY_STATUS_METADATA_KEY = "BIGQUERY_STATUS"
QUERY_STATUS_METADATA_KEY = "STATUS"

CONSUMERS_TO_EXCLUDE = [
    PublisherType.QUERY_ARCHIVER_RETRIEVER.value,
    PublisherType.QUERY_DATA_EXPORT.value,
    PublisherType.QUERY_MIGRATOR.value,
]
