"""
Cloud Function triggered by a Pub/Sub event to hydrate MongoDB with file data.

This function processes a file from a Cloud Storage bucket, filters the data
based on a datetime range, and inserts the processed data into a MongoDB collection.
It handles diagnostics, file parsing, and MongoDB interactions. If the file is successfully
processed, a message is published to a Data Unifier Pub/Sub topic for further processing.
"""

from datetime import time
from typing import Dict, List, Any
from datetime import time
import base64
import json
from bson import ObjectId
import functions_framework
import pandas as pd
from pandas import DataFrame
from pymongo.collection import Collection
from pymongo.errors import BulkWriteError
from const import (
    QUERY_COLLECTION_NAME,
    PROJECT_ID,
    RAC_COLLECTION_NAME_METADATA_KEY,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    CONSUMER_TYPE,
)
from utils.file_util import FileUtils
from utils.mongo_db import get_mongodb_db, get_mongodb_collection, get_mongodb_client
from utils.common_utils import (
    get_query_config,
    generate_collection_name,
    update_query_metadata,
    update_volume,
)
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
    RacTransform,
)


def filter_df_by_date_range(
    df: DataFrame, datetime_column: str, start_datetime: str, end_datetime: str
) -> DataFrame:
    """
    Filters a DataFrame based on a datetime range.

    Parameters:
    - df (pd.DataFrame): The DataFrame to filter.
    - datetime_column (str): The name of the column containing datetime values.
    - start_datetime (str): The start datetime value for the range.
    - end_datetime (str): The end datetime value for the range.

    Returns:
    - pd.DataFrame: The filtered DataFrame.

    Exception Handling:
    - Exception: Raised if an error occurs while filtering dataframe based on the
        specified start and end datetime ranges.
    """
    try:
        # Convert datetime_column to datetime format
        df[datetime_column] = pd.to_datetime(
            df[datetime_column], utc=True, errors="coerce"
        )
        # Convert start_datetime and end_datetime to datetime format
        start_datetime = pd.to_datetime(start_datetime, utc=True)
        end_datetime = pd.to_datetime(end_datetime, utc=True)
        # Filter the DataFrame
        return df[
            (df[datetime_column] >= start_datetime)
            & (df[datetime_column] <= end_datetime)
        ]

    except Exception as e:
        message = f"Failed to filter dataframe based on the specified start and end datetime ranges: {e}"
        raise Exception(message) from e


def insert_dataframe_into_mongodb(df, query_id, mongodb_collection):
    """
    Insert DataFrame records into a MongoDB collection.
    Parameters:
    - df (pd.DataFrame): DataFrame containing the data to be inserted.
    - query_id: The query_id of a query collection document.
    - mongodb_collection (pymongo.collection.Collection): MongoDB collection
        object to insert the data.
    Raises:
    - Exception: If an error occurs while inserting data into MongoDB.
    """
    try:
        df.replace({pd.NA: None, pd.NaT: None}, inplace=True)
        records = df.to_dict(orient="records")

        try:
            mongodb_collection.insert_many(records, ordered=False)
        except BulkWriteError as bwe:
            write_errors = bwe.details.get("writeErrors", [])
            duplicates = [err for err in write_errors if err.get("code") == 11000]

            if duplicates:
                logger.warning(
                    "Duplicate key errors occurred while inserting records for query_id %s: %d duplicates skipped",
                    query_id,
                    len(duplicates)
                )
            else:
                raise bwe

    except Exception as e:
        message = f"Failed to insert DataFrame into MongoDB {mongodb_collection.name} collection for query_id {query_id}: {e}"
        raise Exception(message) from e


def get_ids_from_collection(collection: Collection, field_name: str = "id") -> List:
    """
    Fetches all IDs from a MongoDB collection.

    Args:
        collection: The MongoDB collection object.
        field_name (str): The field containing the IDs (default: '_id').

    Returns:
        list: A list of IDs from the specified field.
    """
    try:
        # Retrieve only the specified ID field from all documents
        ids = collection.find({}, {field_name: 1, "_id": 0})

        # Extract and return the list of IDs
        return [doc[field_name] for doc in ids]

    except Exception as e:
        print(f"An error occurred: {e}")
        return []


def file_dump(
    query_id: str,
    hydration_bucket_name: str,
    mongodb_url: str,
    organization_db_name: str,
    rac_collection_suffix: str,
    file_utils: str,
) -> Dict[str, Any] | None:
    """
    This function dumps the file from the gcp bucket to MongoDB
    - We first get the mongodb client connection
    - get the database and query collection
    - get the query config
    - if we get the query details we set the database name and collection name
    - we get the file content and parse the file
    - Update estimated data size in GB in query metadata
    - Filter the dataframe based on the specified start and end date ranges
    - if all the validation checks passed we insert the data in collection

    Parameters:
    - query_id (str) : Query id of the MongoDB.
    - hydration_bucket_name (str): Hydration bucket name associated with the organization.
    - mongodb_url (str): MongoDB URL associated with the organization.
    - organization_db_name (str): Organization DB name associated with the organization.
    - rac_collection_suffix (str): RAC collection suffix associated with the organization.
    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()
        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )
        query = get_query_config(query_collection, query_id, {"_id": 0})
        query_name = query["name"]

        # Check if query details is not None
        if query is not None:
            data_source_meta_data = {
                "data_source_name": query["source"]["data_source_name"],
            }

            meta_container.set_meta_data(data_source_meta_data)

            # get the meta of the query
            meta_container.update_meta_data(query["meta_data"])
            file_path = meta_container.meta_data["BUCKET_PATH"]
            # TODO: Start and end dates from the user are currently not required but may be re-enabled in the future.
            # start_datetime = meta_container.meta_data["START_DATETIME"]
            # end_datetime = meta_container.meta_data["END_DATETIME"]
            unifier_meta = meta_container.meta_data["UNIFIER_META"]

            # generate collection_name for rac data from the query
            rac_collection_name = generate_collection_name(
                query_id, query_name, rac_collection_suffix
            )
            update_query_metadata(
                query_collection,
                query_id,
                RAC_COLLECTION_NAME_METADATA_KEY,
                rac_collection_name,
            )

            rac_collection = get_mongodb_collection(
                organization_db, rac_collection_name
            )
            id_field = unifier_meta[RacTransform.Tellagence_ID.value]
            rac_collection.create_index([(RacTransform.Tellagence_ID.value, 1)], unique=True)
            id_list = get_ids_from_collection(rac_collection, id_field)

            file_bytes = file_utils.download_file(hydration_bucket_name, file_path)
            file_extension = file_utils.check_file_extension(file_path)

            df = file_utils.parse_file(file_bytes, file_extension)
            # Update estimated data size in GB in query metadata
            estimated_data_size_gb = round(
                df.memory_usage(deep=True).sum() / (1024**3), 6
            )
            update_query_metadata(
                query_collection,
                query_id,
                "ESTIMATED_DATA_SIZE_GB",
                estimated_data_size_gb,
            )
            
            df[RacTransform.Tellagence_ID.value] = df.index
            df = df[~df[id_field].isin(id_list)].reset_index(drop=True)
            # TODO: Start and end dates from the user are currently not required but may be re-enabled in the future.
            # Filter the dataframe for a particular date range
            # df = filter_df_by_date_range(
            #     df, unifier_meta["date"], start_datetime, end_datetime
            # )
            if not df.empty:
                # Convert time columns to string
                df = df.map(lambda x: x.strftime('%H:%M') if isinstance(x,time) else x)
                insert_dataframe_into_mongodb(df, query_id, rac_collection)
            
                logger.info(
                    "DataFrame inserted into MongoDB collection %s successfully.",
                    rac_collection_name,
                )
                update_volume(rac_collection, query_collection, ObjectId(query_id))
 
                return {
                    "status": 200,
                    "message": "successfully dumped file data in rac collection",
                }
            else:
                logger.warning(
                    "No new records to insert for query_id %s. DataFrame is empty after filtering.",
                    query_id
                )
                return {
                    "status": 204,
                    "message": "No new data found to insert into RAC collection",
                }
    except Exception as e:
        message = f"Error occurred during file dump for query_id {query_id}: {e}"
        raise

    finally:
        if mongodb_client is not None:
            mongodb_client.close()


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event):
    """
    Main function where execution starts after receiving a CloudEvent from a pubsub queue.

    This function performs the following steps:
    1. Parses the payload of the CloudEvent from the pubsub queue.
    2. Extracts the query ID from the payload.
    3. Initiates the execution process with the extracted parameters.
    4. If the status of file is not None return status else delete the file

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Raises:
    - Exception: If any error occurs during the execution process.
    """
    try:
        meta_container = MetaContainer()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        org_account_info = OrganizationAccountInfo(organization_id)
        hydration_bucket_name = org_account_info.hydration_bucket_name
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name
        rac_collection_suffix = org_account_info.rac_collection_suffix

        file_utils = FileUtils()

        status = file_dump(
            query_id,
            hydration_bucket_name,
            mongodb_url,
            organization_db_name,
            rac_collection_suffix,
            file_utils,
        )
        if status is not None:
            logger.info("Publishing message to data unifier with payload %s", payload)

            payload["publisher_type"] = CONSUMER_TYPE
            publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)

            message = f"Capabilities File upload hydration script completed successfully for query_id: {query_id}"

            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )
            return status

        file_path = meta_container.meta_data["BUCKET_PATH"]

        message = f"Deleting the file {file_path} on the bucket {hydration_bucket_name}"
        logger.info(message)
        file_utils.delete_file(hydration_bucket_name, file_path)

        return {"status": 400, "error": "Unable to dump the file"}, 400

    except Exception as e:
        message = (
            f"An error occurred during capabilities file upload hydration: {str(e)}"
        )
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        return {"status": 400, "error": message}, 400
