import pandas as pd
from datetime import datetime, timedelta
import calendar
from utils.logger import logger
from utils.utilities import Encapsulation, RacTransform

def generate_iso_calendar_for_date(collection):
    """
    Generate a DataFrame containing ISO calendar information for the minimum and maximum dates in the collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection containing the dates.

    Returns:
    - Tuple: A tuple containing the start and end dates.
      - datetime: The minimum date in the collection.
      - datetime: The maximum date in the collection.

    Raises:
    - Exception: If an error occurs during the generation process.
    """
    try:
        # Aggregate pipeline to find min and max dates
        pipeline = [
            {
                "$group": {
                    "_id": None,
                    "min_date": {"$min": f"${RacTransform.Tellagence_Date.value}"},
                    "max_date": {"$max": f"${RacTransform.Tellagence_Date.value}"},
                }
            }
        ]
        result = collection.aggregate(pipeline).next()
        result["min_date"]
        date_start = result["min_date"]
        date_end = result["max_date"]

        date_df = pd.DataFrame(
            columns=[
                "Date_Time",
                "Date_Time_ISO",
                "Date_Str",
                "Date_Number",
                "Day_Year",
                "WKDay_Name",
                "WKDay_Number",
                "WKNumber_Year",
                "Month_Name",
                "Month_Number",
                "Year",
            ]
        )
        # Date_DF['Date_Time']=pd.date_range(Date_Start,Date_End,freq='d') #-timedelta(days=1) - add only to remove days from the end
        date_df["Date_Time"] = [date_start, date_end]
        date_df["Date_Time_ISO"] = date_df["Date_Time"].map(lambda x: x.isoformat())
        date_df["Date_Str"] = date_df["Date_Time"].dt.strftime("%Y-%m-%d")
        date_df["Date_Number"] = date_df["Date_Str"].str.split("-").str[2]
        date_df["Month_Number"] = date_df["Date_Str"].str.split("-").str[1]
        date_df["Year"] = date_df["Date_Str"].str.split("-").str[0]
        date_df["Month_Name"] = (
            date_df["Month_Number"].astype(int).apply(lambda x: calendar.month_name[x])
        )
        date_df["WKDay_Number"] = (
            date_df["Date_Time"].apply(lambda x: datetime.weekday(x)).astype(str)
        )  # remove .astypr(str) to get int
        date_df["WKDay_Name"] = date_df["Date_Time"].apply(
            lambda x: calendar.day_name[datetime.weekday(x)]
        )
        date_df["Day_Year"] = date_df["Date_Time"].dt.strftime("%j")
        date_df["WKNumber_Year"] = date_df["Date_Time"].dt.strftime("%V")
        date_df["Week_Year"] = date_df["WKNumber_Year"] + "_" + date_df["Year"]
        date_df["Quarter_Number"] = ((date_df["Date_Time"].dt.month - 1) // 3) + 1
        date_df["BiWeekly"] = (
            (date_df["Date_Time"].dt.isocalendar().week - 1) // 2
        ) + 1
        date_df["BiWeekly_StartingWeekNumber"] = (
            date_df["Date_Time"].dt.isocalendar().week
        )

        Unique_Week = list(pd.unique(date_df["WKNumber_Year"]))
        if ("52" in Unique_Week) | ("53" in Unique_Week):
            Ind_A = date_df[
                (date_df["WKNumber_Year"] == "52") | (date_df["WKNumber_Year"] == "53")
            ].index.tolist()
            for nn in range(len(Ind_A)):
                if date_df.loc[Ind_A[nn], "Month_Number"] == "01":
                    date_df.loc[Ind_A[nn], "Year"] = str(
                        int(date_df.loc[Ind_A[nn], "Year"]) - 1
                    )
                    date_df.loc[Ind_A[nn], "Week_Year"] = (
                        date_df.loc[Ind_A[nn], "WKNumber_Year"]
                        + "_"
                        + date_df.loc[Ind_A[nn], "Year"]
                    )
        if ("52" in Unique_Week) | ("53" in Unique_Week):
            Ind_A = date_df[
                (date_df["WKNumber_Year"] == "52") | (date_df["WKNumber_Year"] == "53")
            ].index.tolist()
            if len(Ind_A) == 7:
                date_df.loc[Ind_A, "Week_Year"] = date_df.loc[Ind_A[0], "Week_Year"]
            elif len(Ind_A) > 7:
                cnt1 = 0
                cnt2 = 6
                for _ in range(int(len(Ind_A) / 7)):  # int rounds the number.
                    a = Ind_A[cnt1]
                    b = Ind_A[cnt2]
                    date_df.loc[a:b, "Week_Year"] = date_df.loc[a, "Week_Year"]
                    cnt1 = cnt1 + 7
                    cnt2 = cnt2 + 7
            del Ind_A
        return date_start, date_end
    except Exception as e:
        message = f"Error occurred while generating ISO calendar: {str(e)}"
        raise 


def generate_marker(
    query_config, meta_container, start_date: datetime, end_date: datetime
):
    """
    Generate markers for a given data source and encapsulation cadence.

    Parameters:
    - query_config (dict): Configuration details of the query.
    - meta_container (MetaContainer): meta_container object contains meta data for a query
    - start_date (datetime): The start date for generating markers.
    - end_date (datetime): The end date for generating markers.

    Returns:
    - dict: A dictionary containing the markers and their corresponding start and end dates.

    Raises:
    - Exception: If an error occurs during the marker generation process.
    """
    try:
        query_id = query_config["_id"]
        encapsulation_cadence = meta_container.meta_data[
            "ENCAPSULATION_CADENCE"
        ].lower()
        data_source = query_config["source"]["data_source_name"]
        markers = {}
        current_date = start_date

        while current_date.replace(hour=0, minute=0, second=0) <= end_date:
            year = current_date.year
            month = current_date.month

            match encapsulation_cadence:
                case Encapsulation.DAILY.value:
                    marker_date = current_date.replace(hour=0, minute=0, second=0)
                    marker = f"{query_id}_{data_source.strip().replace(' ', '_').lower()}_{encapsulation_cadence}_{current_date.day}_{month}_{year}"
                    markers[marker] = {
                        "start_date": marker_date.isoformat(),
                        "end_date": (
                            marker_date + timedelta(days=1, microseconds=-1)
                        ).isoformat(),
                    }
                    current_date += timedelta(days=1)
                case Encapsulation.WEEKLY.value:
                    iso_week_number = current_date.isocalendar()[1]
                    start_of_week = current_date - timedelta(
                        days=current_date.isoweekday() - 1
                    )
                    start_of_week = start_of_week.replace(hour=0, minute=0, second=0)
                    end_of_week = start_of_week + timedelta(days=6)
                    end_of_week = end_of_week.replace(hour=0, minute=0, second=0)
                    marker = f"{query_id}_{data_source.strip().replace(' ', '_').lower()}_{encapsulation_cadence}_{iso_week_number}_{year}"
                    markers[marker] = {
                        "start_date": start_of_week.isoformat(),
                        "end_date": (
                            end_of_week + timedelta(days=1, microseconds=-1)
                        ).isoformat(),
                    }
                    current_date = end_of_week + timedelta(days=1)
                case Encapsulation.BIWEEKLY.value:
                    iso_week_number = current_date.isocalendar()[1]
                    start_of_biweekly = current_date - timedelta(
                        days=((current_date.isoweekday() - 1) % 14)
                    )
                    start_of_biweekly = start_of_biweekly.replace(
                        hour=0, minute=0, second=0
                    )
                    end_of_biweekly = start_of_biweekly + timedelta(days=13)
                    end_of_biweekly = end_of_biweekly.replace(
                        hour=0, minute=0, second=0
                    )
                    marker = f"{query_id}_{data_source.strip().replace(' ', '_').lower()}_{encapsulation_cadence}_{iso_week_number}_{year}"
                    markers[marker] = {
                        "start_date": start_of_biweekly.isoformat(),
                        "end_date": (
                            end_of_biweekly + timedelta(days=1, microseconds=-1)
                        ).isoformat(),
                    }
                    current_date = end_of_biweekly + timedelta(days=1)
                case Encapsulation.MONTHLY.value:
                    start_of_month = current_date.replace(day=1)
                    start_of_month = start_of_month.replace(hour=0, minute=0, second=0)
                    end_of_month = start_of_month.replace(
                        day=calendar.monthrange(year, month)[1]
                    )
                    end_of_month = end_of_month.replace(hour=0, minute=0, second=0)
                    marker = f"{query_id}_{data_source.strip().replace(' ', '_').lower()}_{encapsulation_cadence}_{month}_{year}"
                    markers[marker] = {
                        "start_date": start_of_month.isoformat(),
                        "end_date": (
                            end_of_month + timedelta(days=1, microseconds=-1)
                        ).isoformat(),
                    }
                    current_date = end_of_month + timedelta(days=1)
                case Encapsulation.QUARTERLY.value:

                    quarter = (month - 1) // 3 + 1
                    start_of_quarter = current_date.replace(
                        day=1, month=((quarter - 1) * 3) + 1, hour=0, minute=0, second=0
                    )
                    end_of_quarter = start_of_quarter.replace(
                        day=calendar.monthrange(year, start_of_quarter.month + 2)[1],month=(start_of_quarter.month + 2),hour=0, minute=0, second=0
                    )
                    marker = f"{query_id}_{data_source.strip().replace(' ', '_').lower()}_{encapsulation_cadence}_{quarter}_{year}"
                    markers[marker] = {
                        "start_date": start_of_quarter.isoformat(),
                        "end_date": (
                            end_of_quarter + timedelta(days=1, microseconds=-1)
                        ).isoformat(),
                    }
                    current_date = end_of_quarter + timedelta(days=1)
                case Encapsulation.ALLATONCE.value:
                    marker = f"{query_id}_{data_source.strip().replace(' ', '_').lower()}_{encapsulation_cadence}"
                    markers[marker] = {
                        "start_date": start_date.date().isoformat(),
                        "end_date": end_date.isoformat(),
                    }
                    break
                case _:
                    raise Exception(
                        f"Unsupported encapsulation cadence: {encapsulation_cadence}"
                    )
        return markers
    except Exception as e:
        message = f"Error occurred while generating marker: {str(e)}"
        raise


def update_markers_in_collection(markers, collection):
    """
    Update markers into MongoDB.

    Parameters:
    - markers (dict): A dictionary containing the markers and their corresponding start and end dates.
    - collection (pymongo.collection.Collection): Destination MongoDB collection where we want to update the markers.

    Returns:
    - None

    Raises:
    - Exception: If an error occurs during the update process.
    """
    try:
        for marker, dates in markers.items():
            start_date_str = dates["start_date"]
            end_date_str = dates["end_date"]

            start_date = datetime.fromisoformat(start_date_str)
            end_date = datetime.fromisoformat(end_date_str)

            query = {
                RacTransform.Tellagence_Date.value: {
                    "$exists": True,
                    "$ne": None,
                    "$gte": start_date,
                    "$lte": end_date,
                }
            }

            update_result = collection.update_many(
                query, {"$set": {"encapsulation_marker": marker,"is_embedded":False}}
            )
            logger.info(
                f"Updated {update_result.modified_count} documents for marker: {marker}"
            )
        logger.info(
            "Update completed successfully for markers in '%s' collection",
            collection.name,
        )
    except Exception as e:
        message = f"Error occurred while updating markers in {collection.name} collection: {str(e)}"
        raise
