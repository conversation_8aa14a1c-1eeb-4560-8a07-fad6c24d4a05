import os

PROJECTION = {"source.data_source_name": 1, "meta_data": 1}
QUERY_COLLECTION = os.environ.get("QUERY_COLLECTION")
PROJECT_ID = os.environ.get("PROJECT_ID")
ENCAPSULATION_TOPIC_ID = os.getenv("ENCAPSULATION_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)

MAX_RECORD_COUNT = 40_000
MAX_TOKEN_COUNT = 20_000
MAX_INPUT_TEXTS = 250
