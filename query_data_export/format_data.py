import json
import pandas as pd
from bson import ObjectId
import datetime
from utils.logger import logger
from const import DEFAULT_SORT_ORDER
from utils.utilities import Ra<PERSON><PERSON>ransform

def format_conversation_by_time (conversation_by_time_df):
    """
    This method formats the conversation by time results to get the aggregated and formatted data.

    Parameters:
    - conversation_by_time_df (DataFrame): The initial df that is used to group the data

    Returns:
    - DataFrame: The final aggregated df with all the data

    Exception Handling:
    - None
    """
    if conversation_by_time_df.empty:
        return []
    # Extract the date from the _id column and create a new date column
    conversation_by_time_df[RacTransform.Tellagence_Date.value] = conversation_by_time_df["_id"].apply(lambda x: x[RacTransform.Tellagence_Date.value])
    # Drop the `_id` column
    conversation_by_time_df.drop("_id", axis=1, inplace=True)
    return conversation_by_time_df


def format_sentiment_by_time_result(sentiments_by_time_df):
    if not sentiments_by_time_df.empty:
        print("sentiments_by_time_dfffff",sentiments_by_time_df)

        sentiments_by_time_df["date"] = sentiments_by_time_df["_id"]

        exploded_df = sentiments_by_time_df.explode('sentiments', ignore_index=True)

        exploded_df['sentiment'] = exploded_df['sentiments'].apply(lambda x: x['sentiment'])
        exploded_df['count'] = exploded_df['sentiments'].apply(lambda x: x['count'])

        exploded_df = exploded_df.drop(columns=['sentiments'])

        sentiments_by_time_df = exploded_df.pivot(index='date', columns='sentiment', values='count').reset_index().fillna(0)

    return sentiments_by_time_df

def format_video_metrics(df, graph_type, sort_by, sort_order):
    if not df.empty:
        if graph_type == "video":
            df["video"] = df["_id"].apply(lambda x: x["video"])
            df["id"] = df["_id"].apply(lambda x: x["id"])  # Extract video id
        elif graph_type == "creator":
            df["creator"] = df["_id"].apply(lambda x: x["creator"])
        df["sentiment"] = df["_id"].apply(lambda x: x["sentiment"])

        # Drop the '_id' column after normalization
        df = df.drop(columns=["_id"])

        # Group by the graph_type and flatten the data
        match graph_type:
            case "video":
                final_df = (
                    df.groupby(["id", "video", "sentiment"])
                    .sum(numeric_only=True)
                    .reset_index()
                    .pivot(index=["id", "video"], columns="sentiment", values="count")
                    .fillna(0)
                    .reset_index()
                )
            case "creator":
                final_df = (
                    df.groupby(["creator", "sentiment"])
                    .sum(numeric_only=True)
                    .reset_index()
                    .pivot(index="creator", columns="sentiment", values="count")
                    .fillna(0)
                    .reset_index()
                )

        # Add a total_count column
        final_df["total_count"] = final_df.sum(axis=1, numeric_only=True)

        # Sort the data according to the parameters
        if sort_by == "likes" or sort_by == "comment" or sort_by == "reply":
            final_df = final_df.sort_values(
                by="total_count", ascending=(sort_order == "asc")
            )
        elif sort_by == "video_title" or sort_by == "artist_name":
            final_df = final_df.sort_values(
                by="video" if graph_type == "video" else "creator",
                ascending=(sort_order == "asc"),
            )

    return final_df


def format_sentiment_counts(sentiment_counts_df, sort_by, sort_order):
    if not sentiment_counts_df.empty:
        sentiment_counts_df = sentiment_counts_df.rename(columns={"_id": "sentiment"})
        if sort_by and sort_order:
            sentiment_counts_df = sentiment_counts_df.sort_values(
                by=sort_by, ascending=True if sort_order == "asc" else False
            )
        else:
            sentiment_counts_df = sentiment_counts_df.sort_values(
                by="sentiment",
                key=lambda x: x.map(
                    {val: idx for idx, val in enumerate(DEFAULT_SORT_ORDER)}
                ),
            )
    return sentiment_counts_df
