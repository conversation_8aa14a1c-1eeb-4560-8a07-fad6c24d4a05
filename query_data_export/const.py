
import os

QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
PROJECT_ID = os.getenv("PROJECT_ID")

QUERY_DATA_EXPORT_TOPIC_ID = os.getenv("QUERY_DATA_EXPORT_TOPIC_ID")


CONSUMER_TYPE = os.getenv("CONSUMER_TYPE")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")

RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = os.getenv("RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY")
QUERY_DATA_EXPORT_OFFSET_METADATA_KEY = "QUERY_DATA_EXPORT_OFFSET"


LINK_EXPIRATION_TIME = os.getenv("LINK_EXPIRATION_TIME")

QUERY_PROJECTION_ATTRIBUTES = {
    "name": 1,
    "meta_data": 1,
    "source.data_source_id": 1,
    "user_id": 1,
    "RAC_TRANSFORM_COLLECTION_NAME": 1
}

SERVICE_AC = os.getenv("SERVICE_AC")

DEFAULT_SORT_ORDER = ["Positive", "Negative", "Neutral", "No sentiment generated"]

DATA_EXPORT_MESSAGE_TEMPLATE = (
    "We want to inform you that your requested data export for the query: <b>{query_name}</b> (ID: <b>{query_id}</b>) <br>"
    "has been successfully processed and is now ready for download.<br><br>"
    "You can download your data by clicking the link below: <br><br>"
    "<a href='{signed_url}' download style='display:inline-block;padding:10px 15px;background-color:#007BFF;color:white;text-decoration:none;border-radius:5px;'>Download</a><br><br>"
    "Please note that the download link will remain active for <b>{link_exp_hours} hours</b>. We recommend downloading your data before this time.<br>"
)

RAC_TRANSFORM_VOLUME_METADATA_KEY = "RAC_TRANSFORM_VOLUME"


