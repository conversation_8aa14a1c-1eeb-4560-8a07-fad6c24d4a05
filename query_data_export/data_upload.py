import io
import sys
import pandas as pd
from datetime import datetime
from const import (
    BASE_LOOPBACK_THRESHOLD,
    CHUNK_SIZE,
    QUERY_DATA_EXPORT_OFFSET_METADATA_KEY,
    QUERY_DATA_EXPORT_TOPIC_ID,
    SERVICE_AC,
)
from format_data import (
    format_conversation_by_time,
    format_sentiment_by_time_result,
    format_sentiment_counts,
    format_video_metrics,
)
from utils import logger
from utils.common_utils import (
    fetch_collection_aggregated_data,
    update_offset_and_publish,
)
from utils.file_util import FileUtils
from utils.utilities import FileExtension, RacTransform
from google.oauth2 import service_account
from google.cloud import storage
from bson import ObjectId


def upload_chunks_data_to_gcp(
    payload,
    rac_transform_collection,
    chart_type,
    graph_type,
    sort_by,
    sort_order,
    bucket_path,
    bucket_name,
    time_suffix,
    offset,
    offset_metadata_key,
    total_documents,
    query_collection,
    query_id,
    search_query,
):
    logger.info(f"Starting loop with offset: {offset}")
    loopback_threshold = offset + int(BASE_LOOPBACK_THRESHOLD)

    while offset < loopback_threshold:

        logger.info(f"Before fetch, offset is: {offset}")
        pipeline = payload["pipeline"]

        batch_df = fetch_collection_aggregated_data(
            rac_transform_collection, pipeline[:], offset, CHUNK_SIZE
        )

        batch_count = int(len(batch_df))
        logger.info("batch_count: %s", batch_count)

        if batch_count == 0:
            logger.warning("No more records to process. Exiting loop.")
            break

        match chart_type:
            case "pie_chart":
                batch_df = format_sentiment_counts(batch_df, sort_by, sort_order)

            case "conversation_over_time":
                batch_df = format_conversation_by_time(batch_df)

            case "sentiment_over_time":
                batch_df = format_sentiment_by_time_result(batch_df)

            case "video_metrics":
                batch_df = format_video_metrics(
                    batch_df,
                    graph_type,
                    sort_by,
                    sort_order,
                )

        chunk_size = sys.getsizeof(batch_df) / (1024 * 1024)
        logger.info(f"Calculated chunk size: {chunk_size} MB")

        chunk_bucket_path = bucket_path + f"_{offset}_{time_suffix}.csv"
        full_bucket_path = f"gs://{bucket_name}/{chunk_bucket_path}"

        logger.info(f"Generated Bucket Path: {full_bucket_path}")

        add_header = True

        if offset > 0:
            add_header = False

        FileUtils.insert_data_to_gcsfs(
            full_bucket_path,
            batch_df,
            FileExtension.CSV.value,
            add_header=add_header,
        )

        # Convert the datetime object to string before sending loopback message
        if search_query:
            match_stage = payload["pipeline"][1]["$match"]
        else:
            match_stage = payload["pipeline"][0]["$match"]
        match_stage[RacTransform.Tellagence_Date.value]["$gte"] = match_stage[RacTransform.Tellagence_Date.value]["$gte"].isoformat()
        match_stage[RacTransform.Tellagence_Date.value]["$lte"] = match_stage[RacTransform.Tellagence_Date.value]["$lte"].isoformat()
        if RacTransform._id.value in match_stage.keys():
                match_stage[RacTransform._id.value] = str(match_stage[RacTransform._id.value])

        offset = update_offset_and_publish(
            offset,
            offset_metadata_key,
            total_documents,
            loopback_threshold,
            payload,
            batch_count,
            query_collection,
            query_id,
            QUERY_DATA_EXPORT_TOPIC_ID,
        )
        logger.info(f"After fetch, offset remains: {offset}")

        # Convert back to datetime objects if the loopback threshold is not surpassed
        match_stage[RacTransform.Tellagence_Date.value]["$gte"] = datetime.fromisoformat(
            match_stage[RacTransform.Tellagence_Date.value]["$gte"]
        )
        match_stage[RacTransform.Tellagence_Date.value]["$lte"] = datetime.fromisoformat(
            match_stage[RacTransform.Tellagence_Date.value]["$lte"]
        )
        if RacTransform._id.value in match_stage.keys():
            match_stage[RacTransform._id.value] = ObjectId(match_stage[RacTransform._id.value])

        del batch_df


def combine_chunks_and_upload(
    data_export_bucket_name,
    files_path,
    rac_transform_collection_name,
    time_suffix,
    destination_path,
):
    credentials = service_account.Credentials.from_service_account_file(SERVICE_AC)

    storage_client = storage.Client(credentials=credentials)
    bucket = storage_client.bucket(data_export_bucket_name)

    # # List all files matching the operation_name
    blobs = bucket.list_blobs(prefix=files_path)

    csv_blobs = []
    for blob in blobs:
        if (
            blob.name.endswith(".csv")
            and f"{rac_transform_collection_name}_" in blob.name
            and f"{time_suffix}" in blob.name
        ):
            csv_blobs.append(blob.name)

    # Upload combined file to GCP
    destination_blob = bucket.blob(destination_path)

    destination_blob = combine_chunk_files(
        bucket,
        csv_blobs,
        files_path,
        destination_path,
        rac_transform_collection_name,
        time_suffix,
    )

    destination_blob.reload()  # Refresh blob metadata
    final_size = destination_blob.size  # Size in bytes
    logger.info(
        f"Combined file saved to {destination_path} with size: {final_size / (1024 * 1024):.2f} MB"
    )

    return destination_blob


def combine_chunk_files(
    bucket,
    source_files,
    files_path,
    destination_path,
    rac_transform_collection_name,
    time_suffix,
):
    destination_blob = bucket.blob(destination_path)
    if len(source_files) < 2:
        source_blob = bucket.blob(source_files[0])
        process_blob_to_csv_and_upload(bucket, source_blob, destination_path)       
    else:
        while len(source_files) > 32:
            # Combine the first 32 files into an intermediate file
            intermediate_blob_name = f"{files_path}/intermediate_{len(source_files)}_{rac_transform_collection_name}_{time_suffix}.csv"
            intermediate_blob = bucket.blob(intermediate_blob_name)
            intermediate_blob.compose(
                [bucket.blob(file_name) for file_name in source_files[:32]]
            )

            # Delete the source blobs after combining
            for file_name in source_files[:32]:
                bucket.blob(file_name).delete()
            # Replace the first 32 files with the intermediate file
            source_files = [intermediate_blob_name] + source_files[32:]

        # Combine the remaining files into the final destination blob
        if len(source_files)>1:
            destination_blob.compose([bucket.blob(file_name) for file_name in source_files])

    # Delete the final intermediate file
    if len(source_files) == 1 and source_files[0] != destination_path:
        bucket.blob(source_files[0]).delete()

    logger.info(f"All files combined into {destination_path}.")

    return destination_blob

def process_blob_to_csv_and_upload(bucket, source_blob, destination_path):
    """
    Converts a GCS blob to a DataFrame, then uploads the DataFrame as a CSV file to another GCS bucket.

    Args:
        bucket_name (str): Name of the source GCS bucket.
        source_file_name (str): Name of the blob in the source bucket.
        destination_bucket_name (str): Name of the destination GCS bucket.
        destination_file_name (str): Name of the file to save in the destination bucket.
    """

    # Step 1: Download blob data as text and load it into a DataFrame
    blob_data = source_blob.download_as_text()  # Download blob as text
    df = pd.read_csv(io.StringIO(blob_data))    # Load into a pandas DataFrame

    # Step 2: Convert the DataFrame to CSV in-memory
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)  # Write DataFrame to in-memory CSV
    csv_buffer.seek(0)                 # Reset the buffer position

    # Step 3: Upload the CSV directly to the destination bucket
    destination_blob = bucket.blob(destination_path)
    destination_blob.upload_from_string(csv_buffer.getvalue(), content_type="text/csv")

    logger.info(f"CSV successfully uploaded to '{destination_path}'.")
