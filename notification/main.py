from html import unescape
import os
import re
import traceback
import json
import requests
from bson import ObjectId
import base64
import functions_framework
from cloudevents.http import CloudEvent
from pymongo.collection import Collection
from utils.mongo_db import get_mongodb_db, get_mongodb_collection, get_mongodb_client
from utils.logger import logger
from utils.utilities import (
    MessageType,
    MongoDBCollection,
    PublisherType,
    OrganizationAccountInfo,
)
from datetime import datetime, timezone
from sendgrid import SendGridAP<PERSON>lient
from sendgrid.helpers.mail import Mail, To, Email
from jinja2 import Template
from const import (
    SENDGRID_API_KEY,
    SENDGRID_SENDER_EMAIL,
    CAPABILITIES_MONGO_URL,
    CAPABILITIES_DB_NAME,
    CAPABILITIES_APP_URL,
    CONTACT_URL,
    SECRET_KEY,
    NOTIFICATION_API_URL,
    EMAIL_DISPLAY_NAME
)

def get_notification_config(
    collection,
    message_type: str,
    consumer_type: str,
):
    """
    Retrieve notification configuration document from the MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB query collection.
    - projection_attributes (dict): The projection attributes to include or exclude from the result.
    - message_type (str): message type of the notification i.e success of failure
    - consumer_type (str): consumer type for which we have to notify

    Returns:
    - list[dict] or None: The notification configuration document if found, None otherwise.
    """
    try:
        # Retrieve document from the collection based on the message_type, and consumer_type
        records = collection.aggregate(
            [
                {
                    "$match": {
                        "message_type": message_type,
                        "consumer_type": consumer_type,
                    }
                },
                {
                    "$lookup": {
                        "from": MongoDBCollection.NOTIFICATION_TEMPLATE.value,
                        "localField": "notification_template_id",
                        "foreignField": "_id",
                        "as": "notification_template",
                    }
                },
                {"$unwind": {"path": "$notification_template"}},
                {
                    "$lookup": {
                        "from": MongoDBCollection.NOTIFICATION_LAYOUT.value,
                        "localField": "notification_template.layout_id",
                        "foreignField": "_id",
                        "as": "notification_layout",
                    }
                },
                {"$unwind": {"path": "$notification_layout"}},
                {
                    "$project": {
                        "_id": 1,
                        "email": 1,
                        "notification_template.subject": 1,
                        "notification_template.body": 1,
                        "notification_layout.header": 1,
                        "notification_layout.footer": 1,
                        "notification_layout.structure": 1,
                    }
                },
            ]
        )
        return list(records)

    except Exception as e:
        message = f"An error occurred {e} "
        logger.error(message)
        return None


def get_field_value_by_id(
    collection: Collection, record_id: str, field: str, projection: dict
):
    """
    Fetches the value of a specified field from a MongoDB document using record id.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection object.
    - record_id (str): The ID of the document to fetch.
    - field (str): The specific field whose value should be returned.
    - projection (dict): Dictionary specifying the fields to include or exclude in the result.

    Returns:
    - The value of the specified field.

    Exception Handling:
    - Exception: Raised if an error occurs while getting field value.
    """
    try:
        document = collection.find_one({"_id": ObjectId(record_id)}, projection)
        return document[field]

    except Exception as e:
        message = f"Failed to fetch '{field}' for '{record_id}' record ID: {e}"
        raise Exception(message) from e


def send_email(to_email, subject, body):
    """
    Send an email using the SendGrid API.

    Parameters:
        to_email (str): The email address of the recipient.
        subject (str): The subject of the email.
        body (str): The body content of the email.

    Raises:
        Exception: If an error occurs while sending email.

    Returns:
        bool: True if the email is sent successfully, False otherwise.
    """
    try:
        if subject is not None and body is not None:
            message = Mail(
                from_email=Email(SENDGRID_SENDER_EMAIL,EMAIL_DISPLAY_NAME),
                to_emails=To(to_email),
                subject=subject,
                html_content=body,
            )

            sg = SendGridAPIClient(SENDGRID_API_KEY)
            response = sg.send(message)

            logger.info(
                f"Email sent to {to_email}. Status code: {response.status_code}"
            )

        else:
            logger.warning("Email template data is missing. Skipping email sending.")
            return False

        return True
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False


def store_email_logs(
    notification_email_log_collection,
    notification_id,
    notification_config_id,
    email,
    subject,
    body,
):
    """
    Store email logs in the notification_email_log table.

    Parameters:
        notification_email_log_collection (Collection): notification email log collection object
        notification_id (int): The ID of the last notification stored in the notification table.
        notification_config_id (int): The ID stored in the notification_config table for each record.
        email (str): The email address of the recipient.
        subject (str): The subject of the email.
        body (str): The body content of the email.

    Raises:
        Exception: If an error occurs while storing the email log.
    """
    try:
        record_data = {
            "notification_id": notification_id,
            "notification_config_id": notification_config_id,
            "receiver": email,
            "subject": subject,
            "body": body,
            "created_at": datetime.now(timezone.utc),
        }
        notification_email_log_id = notification_email_log_collection.insert_one(
            record_data
        ).inserted_id

        logger.info(
            f"Successfully inserted data into notification_email_log table with ID: {notification_email_log_id}"
        )

    except Exception as e:
        logger.error(f"Error storing email log: {str(e)}")


def post_notification_data(org_id, user_id, notification_ids, secret_key, api_url):
    """
    Sends a POST request with org_id, user_id, notification_ids, and secret_key.

    Args:
        org_id (str): Organization ID.
        user_id (str): User ID.
        notification_ids (list): List of notification IDs.
        secret_key (str): Secret key for authorization or validation.
        api_url (str): The URL of the API to send the request to.

    Returns:
        Response object from the API call.
    """
    # Prepare the payload
    payload = {
        'organization_id': str(org_id),
        'user_id': str(user_id),
        'notification_ids': [str(notification_ids)],
        'secret_key': secret_key
    }

    # Send the POST request
    response = requests.post(api_url, json=payload)

    return response


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Process and handle messages from Pub/Sub.

    This function is designed to handle incoming messages from a Pub/Sub topic. It extracts relevant information from
    the message payload, inserts the data into a MySQL notification table, retrieves configuration details from
    notification_config and notification_template tables, sends emails based on the configuration, store email logs and updates the
    notification status accordingly.

    Parameters:
        message (Pub/Sub Message): The message received from Pub/Sub.

    Raises:
        Exception: If an error occurs during the message processing.

    Notes:
        - The function assumes a certain structure in the message payload, including keys like 'ProducerType',
          'MessageType', 'Message', and 'AdditionalData'.
        - It uses functions like 'send_email','store_email_logs', and 'update_notification_status' to perform specific tasks.
        - Email sending status is tracked, and the notification status is updated to 1 only if all emails are sent
          successfully.
    """
    mongodb_client = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info(f"Message received successfully: {payload}")

        query_id = payload.get("query_id", None)
        organization_id = payload.get("organization_id", None)
        data_source_id = payload.get("data_source_id", None)
        user_id = payload.get("user_id", None)
        consumer_type = payload.get("consumer_type", None)
        message_type = payload.get("message_type", None)
        message_content = payload.get("message", None)
        additional_data = payload.get("additional_data", {})
        variables = additional_data.get("variables", {})
        project_name = variables.get("project_name")
        inviter_name = variables.get("inviter_name")
        project_id = variables.get("project_id")
        token = variables.get("token")
        user_email = variables.get("user_email")
        user_phone_number = variables.get("user_phone_number")

        mongodb_client = get_mongodb_client(CAPABILITIES_MONGO_URL)
        capabilties_db = get_mongodb_db(
            client=mongodb_client, db_name=CAPABILITIES_DB_NAME
        )

        master_data_source_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.MASTER_DATA_SOURCE.value
        )

        notification_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.NOTIFICATION.value
        )

        notification_config_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.NOTIFICATION_CONFIG.value
        )

        notification_email_log_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.NOTIFICATION_EMAIL_LOG.value
        )

        user_info_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.USER_INFO_COLLECTION.value
        )

        organization_users_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.ORGANIZATION_USERS.value
        )
        query_name=""
        if organization_id and query_id:
            # Connect to org db
            org_account_info = OrganizationAccountInfo(organization_id)
            mongodb_url = org_account_info.mongodb_url
            organization_db_name = org_account_info.organization_db_name

            mongodb_client = get_mongodb_client(mongodb_url)
            organization_db = get_mongodb_db(mongodb_client, organization_db_name)
            query_collection = get_mongodb_collection(
                organization_db, MongoDBCollection.QUERY_COLLECTION.value
            )
            query = query_collection.find_one(
                {"_id": ObjectId(query_id)}, {"name": 1}
            )
            query_name=query.get("name", "")
            logger.info("Query Name: %s", query_name)

        if user_id is not None:
            user = user_info_collection.find_one(
                {"_id": ObjectId(user_id)}, {"first_name": 1, "last_name": 1}
            )
            user_full_name = (
                f"{user.get('first_name', '')} {user.get('last_name', '')}".strip()
            )

            user_preferences = organization_users_collection.find_one(
                {
                    "organization_id": ObjectId(organization_id),
                    "user_id": ObjectId(user_id),
                },
                {"notification_preferences": 1, "_id": 0},
            )
        else:
            # Public email notification
            logger.info(
                "Public email notification received for contact us or feedback."
            )
            user_full_name = user_email.split("@")[0] if "@" in user_email else ""
            user_preferences = None

        preferences = (
            user_preferences.get("notification_preferences", {})
            if user_preferences
            else {}
        )

        # # Preferred email
        # preferred_email = preferences.get("preferred_email", None)

        if (
            not preferences.get("enabled", True)
            and not email_enabled
            and not notification_enabled
            and consumer_type != PublisherType.QUERY_DATA_EXPORT.value
            and message_type
            not in [
                "ERROR",
                "SUCCESS",
                "WELCOME",
                "CONTACT",
                "FEEDBACK",
                "RESET_PASSWORD",
            ]
        ):
            logger.info(
                f"Notifications are disabled for user: {user_id} in organization: {organization_id}"
            )
            return

        notification_type = get_notification_type(message_type, consumer_type)

        email_enabled, notification_enabled = get_notification_status(
            preferences, notification_type
        )

        record_data = {
            "query_id": ObjectId(query_id) if query_id else None,
            "organization_id": ObjectId(organization_id) if organization_id else None,
            "data_source_id": ObjectId(data_source_id) if data_source_id else None,
            "user_id": ObjectId(user_id) if user_id else None,
            "consumer_type": consumer_type,
            "message_type": message_type,
            "message": message_content,
            "additional_data": additional_data,
            "status": 0,
            "created_at": datetime.now(timezone.utc),
            "name": project_name or None,
            "inviter_name": inviter_name or None,
            "project_id": ObjectId(project_id) if project_id else None,
            "email_enabled": email_enabled,
            "notification_enabled": notification_enabled,
        }

        # Add check to prevent multiple same notification/emails - query related
        if query_id and message_type in ["ERROR", "SUCCESS"]:
            check_attributes = {
                "query_id": ObjectId(query_id),
                "organization_id": ObjectId(organization_id) if organization_id else None,
                "data_source_id": ObjectId(data_source_id) if data_source_id else None,
                "user_id": ObjectId(user_id) if user_id else None,
                "consumer_type": consumer_type,
                "message_type": message_type,
                "message": message_content,
            }
            same_notification_count = notification_collection.count_documents(check_attributes)
            if same_notification_count > 0:
                return

        # Check if notification and email for the specified message_type is enabled
        # If either is enabled, create the notificaton record

        last_notification_id = notification_collection.insert_one(
            record_data
        ).inserted_id

        # Post notification data to the API if user preferences are set
        if notification_enabled:
            logger.info(f"Sending live notification to user_id: {user_id}")
            post_notification_data(
                organization_id,
                user_id,
                last_notification_id,
                SECRET_KEY,
                NOTIFICATION_API_URL,
            )

        config_results = get_notification_config(
            notification_config_collection,
            message_type,
            consumer_type,
        )
        # Send emails and store logs
        if config_results:
            # Flag to track email sending status
            all_emails_sent = True

            for config_result in config_results:
                notification_config_id = config_result["_id"]
                email = config_result["email"]
                subject = config_result["notification_template"]["subject"]
                body = config_result["notification_template"]["body"]
                header = config_result["notification_layout"]["header"]
                footer = config_result["notification_layout"]["footer"]
                structure = config_result["notification_layout"]["structure"]

                # Retrieve the data source name to dynamically populate the email subject.
                data_source_name = None
                if data_source_id is not None:
                    data_source_name = get_field_value_by_id(
                        master_data_source_collection,
                        data_source_id,
                        "name",
                        {"_id": 0, "name": 1},
                    )
                subject = subject.format(
                    data_source_name=(data_source_name or "").upper(),
                    consumer_type=consumer_type,
                    inviter=inviter_name or "",
                    user=user_full_name or "",
                )

                # Retrieve the user's email based on their user ID to send an email for INFO message type.
                # Check if email for the specified message_type is enabled or if the consuer_type is QUERY_DATA_EXPORT
                if (
                    (
                        config_result["email"] in [None, ""]
                        and message_content is not None
                        and email_enabled
                    )
                    or consumer_type == PublisherType.QUERY_DATA_EXPORT.value
                    or (
                        message_type
                        in [MessageType.CONTACT.value, MessageType.FEEDBACK.value, MessageType.WELCOME.value, MessageType.RESET_PASSWORD.value]
                        and config_result["email"] in [None, ""]
                    )
                    or (
                        message_type == MessageType.ERROR.value
                        and email_enabled
                        and config_result["email"] in [None, ""]
                    )
                ):
                    if user_id is not None:
                        email = get_field_value_by_id(
                            user_info_collection,
                            user_id,
                            "email",
                            {"_id": 0, "email": 1},
                        )
                        # if preferred_email not in [None, ""]:
                        #     email = preferred_email
                    else:
                        email = user_email

                    page_url = get_page_url(
                        message_type, token, query_id, consumer_type, message_content
                    )
                    logger.info(f"Page url: {page_url}")

                    template = Template(body)
                    body = template.render(
                        message_content=message_content,
                        app_url=CAPABILITIES_APP_URL,
                        contact_url=CONTACT_URL,
                        year=datetime.now().year,
                        username=user_full_name or "",
                        page_url=page_url or "",
                        project_name=project_name or "",
                        inviter_name=inviter_name or "",
                        query_name=query_name or "",
                        message=message_content,
                        admin_name="TELLAGENCE ADMIN",
                        email=user_email or "",
                        phone_number=user_phone_number or "",
                    )
                    template = Template(structure)
                    structure = template.render(
                        header=header,
                        dynamic_content=body,
                        footer=footer,
                    )

                elif (
                    query_id is not None
                    and organization_id is not None
                    and data_source_id is not None
                    and consumer_type is not None
                    and message_content is not None
                    and config_result["email"] not in [None, ""]
                ) or (
                    message_type
                    in [MessageType.CONTACT.value, MessageType.FEEDBACK.value]
                    and config_result["email"] not in [None, ""]
                ):
                    template = Template(body)
                    body = template.render(
                        query_id=query_id,
                        organization_id=organization_id,
                        data_source_id=data_source_id,
                        consumer_type=consumer_type,
                        message_content=message_content,
                        message=message_content,
                        admin_name="TELLAGENCE ADMIN",
                        email=user_email or "",
                        phone_number=user_phone_number or "",
                        username=user_full_name or "",
                    )
                    template = Template(structure)
                    structure = template.render(
                        header=header,
                        dynamic_content=body,
                        footer=footer,
                    )
                    email = config_result["email"]

                if email:
                    if isinstance(email, str):
                        email = [email]

                    for current_email in email:
                        if not send_email(current_email, subject, structure):
                            all_emails_sent = False

                        store_email_logs(
                            notification_email_log_collection,
                            last_notification_id,
                            notification_config_id,
                            current_email,
                            subject,
                            structure,
                        )
                else:
                    logger.warning("Empty email field found in config.")

            # Update Status column to 1 if all emails are sent successfully
            if all_emails_sent:
                notification_collection.update_one(
                    {"_id": last_notification_id}, {"$set": {"status": 1}}
                )
            else:
                logger.warning(
                    f"Not updating Status to 1 for notification ID: {last_notification_id}"
                )
        else:
            logger.warning(
                f"No config found for message_type: {message_type}, and consumer_type: {consumer_type}"
            )

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error while notifying: {e}")
    finally:
        if mongodb_client is not None:
            mongodb_client.close()


def get_notification_status(preferences, category):
    """
    Returns a tuple (email_enabled, notification_enabled) for a given notification category.
    """
    if not preferences.get("enabled", False):
        return (False, False)

    category_settings = preferences.get(category, {})
    email_enabled = category_settings.get("email", False)
    notification_enabled = category_settings.get("notification", False)

    return (email_enabled, notification_enabled)


def get_notification_type(message_type, consumer_type):
    NOTIFICATION_TYPE_MAP = {
        ("INFO", "SEQUENCE_COORDINATOR"): "query_completion",
        ("ERROR", None): "query_interruption",
        ("SHARED_QUERY", None): "shared_query",
        ("PROJECT_INVITE", None): "shared_project",
        ("WELCOME", None): "welcome",
    }

    # Try exact match
    key = (message_type, consumer_type)
    if key in NOTIFICATION_TYPE_MAP:
        return NOTIFICATION_TYPE_MAP[key]

    # Try fallback match with consumer_type=None
    key = (message_type, None)
    return NOTIFICATION_TYPE_MAP.get(key)


def get_page_url(
    message_type, token=None, query_id=None, consumer_type=None, message=None
):
    """
    Returns the page URL based on the message type.
    """
    match message_type:
        case MessageType.PROJECT_INVITE.value:
            return f"{CAPABILITIES_APP_URL}/my-projects"
        case MessageType.SHARED_QUERY.value:
            return f"{CAPABILITIES_APP_URL}/shared-queries"
        case MessageType.WELCOME.value:
            return f"{CAPABILITIES_APP_URL}/signup?token={token}"
        case MessageType.RESET_PASSWORD.value:
            return f"{CAPABILITIES_APP_URL}/reset-password?token={token}"
        case MessageType.CONTACT.value:
            return f"{CAPABILITIES_APP_URL}/faqs"
        case MessageType.INFO.value:
            if consumer_type == PublisherType.QUERY_DATA_EXPORT.value:
                message = unescape(message)

                signed_url = re.search(r"<a href='(.*?)'", message)
                signed_url = signed_url.group(1) if signed_url else None

                return signed_url

            else:
                return f"{CAPABILITIES_APP_URL}/dashboard?query_id={query_id}"
        case _:
            return CAPABILITIES_APP_URL