[{"name": "Queries", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/queries", "params": {"page": 1, "limit": 10, "sort_by": "created_at", "sort_order": "desc", "query_type": "my_queries"}}, {"name": "Projects", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/projects/", "params": {"page": "1", "limit": "10", "sort_by": "created_at", "sort_order": "desc"}}, {"name": "Project Metrics", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/projects/metrics", "params": {}}, {"name": "Notifications", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/notifications", "params": {"page": 1, "limit": 10}}, {"name": "Interpretations", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/overview/interpretations", "params": {"query_id": "{query_id}"}}, {"name": "Metrics", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/metrics/{query_id}", "params": {"start_date": "2024-03-18T18:30:00.000Z", "end_date": "2025-01-02T18:29:59.999Z", "data_source_type": "File Upload"}, "cases": ["default", "sentiment", "search"]}, {"name": "Post", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/analysis/post/{query_id}", "params": {"start_date": "2024-05-31T18:30:00.000Z", "end_date": "2024-08-01T18:29:59.999Z", "data_source_type": "File Upload", "page": "1", "limit": "20"}, "cases": ["default", "sentiment", "search"]}, {"name": "Activity", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/overview/activity/{query_id}", "params": {"start_date": "2024-05-31T18:30:00.000Z", "end_date": "2024-08-01T18:29:59.999Z", "data_source_type": "File Upload", "page": "1", "limit": "20"}, "cases": ["default", "sentiment", "search"]}, {"name": "Sentiment", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/sentiments/{query_id}", "params": {"start_date": "2024-05-31T18:30:00.000Z", "end_date": "2024-08-01T18:29:59.999Z", "data_source_type": "File Upload"}, "cases": ["default", "sentiment", "search"]}, {"name": "Themes", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/analysis/context/theme/", "params": {"query_id": "{query_id}", "encapsulation_marker": "{encapsulation_marker}", "page": "1", "limit": "25"}, "cases": ["default", "additional_filter", "search"]}, {"name": "Stories", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/analysis/context/story/", "params": {"query_id": "{query_id}", "encapsulation_marker": "{encapsulation_marker}", "page": "1", "limit": "25", "parent_id": "0"}, "cases": ["default", "additional_filter", "search"]}, {"name": "Cluster", "method": "GET", "url": "https://rc-api.tellagence.ai/api/v1/analysis/context/cluster/", "params": {"query_id": "{query_id}", "encapsulation_marker": "{encapsulation_marker}", "page": "1", "limit": "25", "parent_id": "0", "theme": "0"}, "cases": ["default", "additional_filter", "search"]}]