# config.py
LOGIN_URL = "https://rc-api.tellagence.ai/api/v1/auth/login"
EMAIL = "<EMAIL>"
PASSWORD = "12345678"

SWITCH_URL = "https://rc-api.tellagence.ai/api/v1/organization/switch"

DEFAULT_HEADERS = {
    "accept": "*/*",
    "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
    "origin": "https://rc.tellagence.ai",
    "priority": "u=1, i",
    "referer": "https://rc.tellagence.ai/",
    "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Linux\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"
}

QUERY_OPTIONS = {
    "1": {
        "query_id": "68835836791cf644f546a04f",
        "volume": "55803"
    },
    "2": {
        "query_id": "66f1b9d5f1b6d4dd34b57ede",
        "volume": "120809"
    },
    "3": {
        "query_id": "67d9ef6178ade9c7d811ded8",
        "volume": "200000"
    },
    "4": {
        "query_id": "671bcef47eeae016395483cd",
        "volume": "418886"
    },
    "5": {
        "query_id": "6799c01eea791d985b80eb3a",
        "volume": "236935"
    }
}

QUERY_CONFIG = {
    "68835836791cf644f546a04f": {
        "volume": "55803",
        "organization_id": "66b62f787d3eda5b857bb329",
        "start_date": "2025-04-21T18:30:00.000Z",
        "end_date": "2025-05-09T18:29:59.999Z",
        "data_source_type": "YouTube Comments",
        "query_id": "68835836791cf644f546a04f",
        "encapsulation_marker": "68835836791cf644f546a04f_youtube_comments_allatonce",
        "sentiment": "Neutral",
        "search": "youtube OR first",
        "additional_filter": "[{\"name\":\"sentiment\",\"values\":[\"Positive\"],\"data_type\":\"text\"}]"  
    },
    "66f1b9d5f1b6d4dd34b57ede": {
        "volume": "120809",
        "organization_id": "66bb463ea948d0caa80f1281",
        "start_date": "2024-05-31T18:30:00.000Z",
        "end_date": "2024-08-01T18:29:59.999Z",
        "data_source_type": "File Upload",
        "query_id": "66f1b9d5f1b6d4dd34b57ede",
        "encapsulation_marker": "66f1b9d5f1b6d4dd34b57ede_file_upload_allatonce",
        "sentiment": "Neutral",
        "search": "youtube OR tv",
        "additional_filter": "[{\"name\":\"sentiment\",\"values\":[\"Positive\"],\"data_type\":\"text\"}]"  
    },
    "687a1a9ef6e84e58dbfd84fd": {
        "volume": "200000",
        "organization_id": "66b62f787d3eda5b857bb329",
        "start_date": "2024-03-18T18:30:00.000Z",
        "end_date": "2025-01-02T18:29:59.999Z",
        "data_source_type": "File Upload",
        "query_id": "687a1a9ef6e84e58dbfd84fd",
        "encapsulation_marker": "687a1a9ef6e84e58dbfd84fd_file_upload_allatonce",
        "sentiment": "Neutral",
        "search": "food OR online",
        "additional_filter": "[{\"name\":\"sentiment\",\"values\":[\"Positive\"],\"data_type\":\"text\"}]"  
    },
    "671bcef47eeae016395483cd": {
        "volume": "418886",
        "organization_id": "66bb463ea948d0caa80f1281",
        "start_date": "2023-02-23T18:30:00.000Z",
        "end_date": "2024-10-29T18:29:59.999Z",
        "data_source_type": "YouTube Comments",
        "query_id": "671bcef47eeae016395483cd",
        "encapsulation_marker": "671bcef47eeae016395483cd_youtube_comments_allatonce",
        "sentiment": "Neutral",
        "search": "amo OR online",
        "additional_filter": "[{\"name\":\"sentiment\",\"values\":[\"Positive\"],\"data_type\":\"text\"}]"  
    },
    "6799c01eea791d985b80eb3a": {
        "volume": "236935",
        "organization_id": "66b62f787d3eda5b857bb329",
        "start_date": "2025-01-19T18:30:00.000Z",
        "end_date": "2025-01-27T18:29:59.999Z",
        "data_source_type": "File Upload",
        "query_id": "6799c01eea791d985b80eb3a",
        "encapsulation_marker": "6799c01eea791d985b80eb3a_file_upload_allatonce",
        "sentiment": "Neutral",
        "search": "DeepSeek OR model",
        "additional_filter": "[{\"name\":\"sentiment\",\"values\":[\"Positive\"],\"data_type\":\"text\"}]",
    }
}