import shlex
import json

def extract_headers_from_curl(curl_command):
    tokens = shlex.split(curl_command)
    headers = {}

    i = 0
    while i < len(tokens):
        if tokens[i] == '-H':
            header = tokens[i+1]
            key, value = header.split(':', 1)
            headers[key.strip()] = value.strip()
            i += 1
        i += 1

    return headers

curl_cmd = """
"""

headers = extract_headers_from_curl(curl_cmd)

# Print headers as pretty JSON
print("Extracted Headers (JSON):")
print(json.dumps(headers, indent=4))
