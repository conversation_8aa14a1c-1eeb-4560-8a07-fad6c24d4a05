import json
import shlex
from urllib.parse import urlparse, parse_qs

def extract_params_from_curl(curl_command):
    tokens = shlex.split(curl_command)
    url = ""

    for token in tokens:
        if token.startswith("http"):
            url = token
            break

    if not url:
        return {}

    parsed_url = urlparse(url)
    params = parse_qs(parsed_url.query)

    # Flatten the params (since parse_qs gives lists for each value)
    flat_params = {k: v[0] if len(v) == 1 else v for k, v in params.items()}

    return flat_params

curl_cmd = """
"""
params = extract_params_from_curl(curl_cmd)
print("Extracted Parameters (JSON):")
print(json.dumps(params, indent=4))