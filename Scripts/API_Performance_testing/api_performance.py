import copy
import csv
import os
import requests
import json
import time
import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

def get_auth_token(org_id):
    """
    Logs in using credentials from config.py and retrieves the token.
    Assumes login API returns token in JSON response under 'token' key.
    """
    login_payload = {
        "email": config.EMAIL,
        "password": config.PASSWORD
    }

    try:
        response = requests.post(config.LOGIN_URL, json=login_payload)
        response.raise_for_status()
        token = response.json().get('access_token')

        headers = config.DEFAULT_HEADERS
        headers['authorization'] = f"Bearer {token}"

        response = requests.post(config.SWITCH_URL, json={"organization_id": org_id}, headers=headers)
        response.raise_for_status()
        token = response.json().get('access_token')

        if not token:
            raise Exception("Token not found in login response.")
        
        logging.info(f"Successfully logged in")
        return token
    except Exception as e:
        logging.exception(f"Process failed: {e}")


def hit_apis(token, selected_query_id, selected_api=None):
    with open('api_list.json', 'r') as file:
        api_list = json.load(file)

    csv_file = "api_results.csv"
    write_header = not os.path.exists(csv_file)

    with open(csv_file, mode='a', newline='') as csvfile:
        fieldnames = ['api_name', 'volume', 'filter','status_code', 'response_time_sec', 'timestamp']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        if write_header:
            writer.writeheader()

        for api in api_list:
            name = api.get('name', 'Unnamed API')

            if selected_api and name.lower() != selected_api.lower():
                continue

            method = api.get('method', 'GET').upper()
            url = api.get('url')
            params = api.get('params', {})
            headers = api.get('headers', config.DEFAULT_HEADERS)
            body = api.get('body', None)
            cases = api.get('cases', ["default"])

            # Format params based on query_id if applicable
            params = set_params(params, selected_query_id)

            # Inject Authorization token into headers
            headers['authorization'] = f"Bearer {token}"
            url = url.format(query_id=selected_query_id)

            logging.info(f"\n--- Hitting API: {name} ---")
            logging.info(f"initial params: {json.dumps(params, indent=4)}")

            start_time = time.time()
            for case in cases:

                # Create a fresh copy of params for each case
                case_params = copy.deepcopy(params)
                if case != "default":
                    case_params[case] = config.QUERY_CONFIG[selected_query_id][case]
                    logging.info(f"final params: {json.dumps(case_params, indent=4)}")

                try:
                    response = requests.request(
                        method,
                        url,
                        headers=headers,
                        params=case_params,
                        json=body if headers.get('Content-Type') == 'application/json' else None,
                        data=None if headers.get('Content-Type') == 'application/json' else body
                    )
                    end_time = time.time()

                    elapsed_time_sec = round(end_time - start_time, 2)
                    status_code = response.status_code

                    logging.info(f"Url: {url}")
                    logging.info(f"Method: {method}")
                    logging.info(f"Headers: {json.dumps(headers, indent=4)}")
                    logging.info(f"Params: {json.dumps(case_params, indent=4)}")

                    logging.info(f"Response Time: {elapsed_time_sec} sec")
                    logging.info(f"Response Status Code: {status_code}")
                    logging.info(f"Response Body: {response.text[:200]}...") 

                    # Append to CSV
                    writer.writerow({
                        'api_name': name,
                        'volume': config.QUERY_CONFIG[selected_query_id]['volume'],
                        'filter': case,
                        'status_code': status_code,
                        'response_time_sec': elapsed_time_sec,
                        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                    })

                except Exception as e:
                    logging.exception(f"Request failed: {e}")


def set_params(params, query_id):
    """
    Sets parameters based on the selected query_id.
    """
    query_config = config.QUERY_CONFIG[query_id]
    for key in ["start_date", "end_date", "data_source_type", "query_id", "encapsulation_marker"]:
        if key in params and key in query_config:
            params[key] = query_config[key]

    return params


if __name__ == "__main__":
    print(json.dumps(config.QUERY_OPTIONS, indent=4))
    choice = input("Enter your choice (1-4): ").strip()

    rounds = int(input("Enter number of rounds to run: ").strip())

    selected_api = input("Provide specific API Name or 'all': ").strip()

    if selected_api.lower() == 'all':
        selected_api = None

    if choice in config.QUERY_OPTIONS:
        for i in range(rounds):
            logging.info(f"\nRound {i+1} of {rounds}")
            selected_query_id = config.QUERY_OPTIONS[choice]['query_id']
            org_id=config.QUERY_CONFIG[selected_query_id]['organization_id']
            token = get_auth_token(org_id)
            hit_apis(token, selected_query_id, selected_api)
    else:
        logging.error("Invalid Query ID selected.")
