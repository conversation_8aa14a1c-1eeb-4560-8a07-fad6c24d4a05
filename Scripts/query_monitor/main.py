import pytz
from datetime import datetime, timed<PERSON>ta
from bson import ObjectId
from pymongo import MongoClient
from googleapiclient.discovery import build
from google.auth import default
from utils.utilities import OrganizationAccountInfo
from email_utils import send_query_summary_email
from utils.logger import logger
from utils.utilities import QueryStatus
import functions_framework
from const import (
    ORG_URL,
    PLATFORM_DB_URI,
    PLATFORM_DB_NAME,
    SHEET_ID,
    TIMEZONE,
    HOUR,
    MINUTE,
    SECOND,
    PAST_DAYS,
    QUERY_COLLECTION,
    DIAGNOSTIC_COLLECTION,
    ORGANIZATION_DOMAIN_INFORMATION_COLLECTION,
    ORGANIZATION_DATA_SOURCE,
    HEADER_LENGTH,
    EXCLUDE_EMAIL_PATTERN,
    RESTRICT_EMAIL_TIME,
    EMAIL_WINDOW_MINUTES,
)


def format_number_short(n):
    """
    Convert a number to a short readable format (K, M, B).

    Parameters:
    - n (int): The number to format.

    Returns:
    - str: Formatted string.

    Exception Handling:
    - None
    """
    if n:
        if n >= 1_000_000_000:
            return f"{n / 1_000_000_000:.1f}B"
        elif n >= 1_000_000:
            return f"{n / 1_000_000:.1f}M"
        elif n >= 1_000:
            return f"{n / 1_000:.1f}K"
    return str(n)


def get_current_datetime():
    """
    Get current datetime
    """
    tz = pytz.timezone(TIMEZONE)
    now = datetime.now(tz)
    return now


def get_google_sheet_service():
    """
    Initialize Google Sheets API service.

    Parameters:
    - None

    Returns:
    - googleapiclient.discovery.Resource: Google Sheets service instance.

    Exception Handling:
    - None
    """
    creds, _ = default(scopes=["https://www.googleapis.com/auth/spreadsheets"])
    return build("sheets", "v4", credentials=creds)


def compute_report_time_range():
    """
    Calculate the report's start and end datetime window.

    Parameters:
    - None

    Returns:
    - tuple: (start_time, end_time) as timezone-aware datetime objects.

    Exception Handling:
    - None
    """
    current_date = get_current_datetime()
    target = current_date.replace(
        hour=HOUR, minute=MINUTE, second=SECOND, microsecond=0
    )

    return target - timedelta(days=PAST_DAYS), target


def delete_and_recreate_sheet_tab(service, title):
    """
    Delete an existing sheet tab if it exists, then recreate it.

    Parameters:
    - service: Google Sheets API service instance.
    - title (str): The title of the sheet tab.

    Returns:
    - dict: Updated mapping of sheet titles to their sheet IDs.

    Exception Handling:
    - None
    """
    metadata = service.spreadsheets().get(spreadsheetId=SHEET_ID).execute()
    sheets = {
        s["properties"]["title"]: s["properties"]["sheetId"] for s in metadata["sheets"]
    }
    if title in sheets:
        service.spreadsheets().batchUpdate(
            spreadsheetId=SHEET_ID,
            body={"requests": [{"deleteSheet": {"sheetId": sheets[title]}}]},
        ).execute()
    service.spreadsheets().batchUpdate(
        spreadsheetId=SHEET_ID,
        body={"requests": [{"addSheet": {"properties": {"title": title}}}]},
    ).execute()
    updated = service.spreadsheets().get(spreadsheetId=SHEET_ID).execute()
    return {
        s["properties"]["title"]: s["properties"]["sheetId"] for s in updated["sheets"]
    }


def get_excluded_query_ids(service, existing_titles, current_title):
    """
    Fetch previously recorded query IDs to avoid duplicates.

    Parameters:
    - service: Google Sheets API service instance.
    - existing_titles (dict): Existing sheet tabs.
    - current_title (str): Title of the current day's sheet.

    Returns:
    - set: A set of query IDs to exclude.

    Exception Handling:
    - None
    """
    # Retrieves query IDs from the latest past sheet tab to avoid reprocessing
    exclude_ids = set()
    date_titles = [
        (t, datetime.strptime(t, "%Y-%m-%d"))
        for t in existing_titles
        if t < current_title and is_date_title(t)
    ]
    if date_titles:
        prev_title = sorted(date_titles, key=lambda x: x[1])[-1][0]
        result = (
            service.spreadsheets()
            .values()
            .get(spreadsheetId=SHEET_ID, range=f"'{prev_title}'!A2:A")
            .execute()
        )
        for row in result.get("values", []):
            if row:
                exclude_ids.add(row[0].strip())
    return exclude_ids


def is_date_title(title):
    """
    Check if a sheet title matches the date format.

    Parameters:
    - title (str): Sheet title.

    Returns:
    - bool: True if the title is in YYYY-MM-DD format.

    Exception Handling:
    - Returns False if the title is not a valid date.
    """
    try:
        datetime.strptime(title, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def append_query_data_to_sheet(
    service, sheet_tab_name, org_ids, exclude_query_ids, start_time, end_time
):
    """
    Process and append query data to the current sheet tab.

    Parameters:
    - service: Google Sheets API service instance.
    - sheet_tab_name (str): Title of the current tab.
    - org_ids (list): List of organization IDs.
    - exclude_query_ids (set): Query IDs to skip.
    - start_time (datetime): Start of time range.
    - end_time (datetime): End of time range.

    Returns:
    - tuple: (completed, failed, pending) query counts.

    Exception Handling:
    - None
    """
    client = MongoClient(ORG_URL)
    platform_client = MongoClient(PLATFORM_DB_URI)
    platform_db = platform_client[PLATFORM_DB_NAME]
    header = [
        "Query Id",
        "Query Name",
        "Username",
        "Status",
        "Organization",
        "Data Source",
        "Cadence",
        "Volume",
        "Start Time",
        "End Time",
        "Total Time",
        "Comments",
    ]

    service.spreadsheets().values().update(
        spreadsheetId=SHEET_ID,
        range=f"'{sheet_tab_name}'!A1",
        valueInputOption="RAW",
        body={"values": [header]},
    ).execute()

    completed = failed = pending = blocked = 0
    rows = []

    # Fetching organization information from vault
    for org_id in org_ids:
        org_info = OrganizationAccountInfo(str(org_id))

        logger.info(f"Processing : {org_info.organization_name}")

        db = client[org_info.organization_db_name]
        query_col = db[QUERY_COLLECTION]

        # Aggregate all queries created within the time window for this org
        queries = list(
            query_col.aggregate(
                [
                    {
                        "$match": {
                            "name": {"$exists": True, "$ne": ""},
                            "created_at": {"$gte": start_time, "$lt": end_time},
                            "meta_data.USER_EMAIL": {
                                "$not": {
                                    "$regex": EXCLUDE_EMAIL_PATTERN,
                                    "$options": "i",
                                }
                            },
                            "meta_data.IS_HARD_DELETED": {"$ne": True},
                        }
                    },
                    {
                        "$lookup": {
                            "from": ORGANIZATION_DATA_SOURCE,
                            "localField": "source_id",
                            "foreignField": "_id",
                            "as": "source",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$source",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                ]
            )
        )

        for query in queries:
            query_id = str(query["_id"])
            if query_id in exclude_query_ids:
                continue

            # Determine final query status
            status = determine_status(query)

            # Update status counters
            completed += status == QueryStatus.READY.value
            failed += status == QueryStatus.FAILED.value
            pending += status == QueryStatus.PENDING.value
            blocked += status == QueryStatus.QUOTA_EXHAUSTED.value

            # Fetch diagnostics (execution logs) from Platform DB
            diag_docs = list(
                platform_db[DIAGNOSTIC_COLLECTION].find(
                    {"query_id": ObjectId(query_id)}
                )
            )
            start_str, end_str, total_time = compute_diagnostic_time(diag_docs, status)
            # Fetch message from diagnostics for failed queries if available
            comment = next(
                (
                    diag_doc.get("message")
                    for diag_doc in diag_docs
                    if diag_doc.get("status") == QueryStatus.FAILED.value
                    and diag_doc.get("message")
                ),
                "",
            )

            # Prepare row for Google Sheet
            row = [
                query_id,
                query.get("name", ""),
                f"{query.get('meta_data', {}).get('USER_FIRST_NAME', '')} {query.get('meta_data', {}).get('USER_LAST_NAME', '')}".strip(),
                status,
                org_info._account_info.get("organization_name"),
                query.get("source", {}).get("data_source_name", ""),
                query.get("meta_data", {}).get("ENCAPSULATION_CADENCE", ""),
                format_number_short(query.get("meta_data", {}).get("RAC_VOLUME", "")),
                start_str,
                end_str,
                total_time,
                comment,
            ]
            rows.append([str(x) for x in row])

    if rows:
        # Append all collected rows to the sheet (below header)
        service.spreadsheets().values().append(
            spreadsheetId=SHEET_ID,
            range=f"'{sheet_tab_name}'!A2",
            valueInputOption="USER_ENTERED",
            insertDataOption="INSERT_ROWS",
            body={"values": rows},
        ).execute()

    return completed, failed, pending, blocked


def determine_status(query):
    """
    Determine the final status of a query.

    Parameters:
    - query (dict): Query document.

    Returns:
    - str: One of 'COMPLETED', 'FAILED', 'QUOTA_EXHAUSTED', or 'PENDING'.

    Exception Handling:
    - None
    """
    query_status = query.get("meta_data", {}).get("STATUS", "")
    return query_status


def compute_diagnostic_time(docs, status):
    """
    Compute the time duration of query execution from diagnostics.

    Parameters:
    - docs (list): Diagnostic documents.
    - status (str): Status of the query.

    Returns:
    - tuple: (start_time_str, end_time_str, total_duration_str)

    Exception Handling:
    - None
    """
    # Extracts earliest start and latest end timestamps from diagnostics
    start_dates = [doc.get("start_date") for doc in docs if doc.get("start_date")]
    end_dates = [doc.get("end_date") for doc in docs if doc.get("end_date")]

    if start_dates:
        start = min(start_dates).strftime("%Y-%m-%d %H:%M:%S")
        if status in [
            QueryStatus.PENDING.value,
            QueryStatus.FAILED.value,
            QueryStatus.QUOTA_EXHAUSTED.value,
        ]:
            return (start, "", "")
        if end_dates:
            end = max(end_dates)
            delta = end - min(start_dates)
            return (
                start,
                end.strftime("%Y-%m-%d %H:%M:%S"),
                f"{delta.days} days, {delta.seconds // 3600} hours, {(delta.seconds % 3600) // 60} minutes",
            )

    return ("", "", "")


def apply_sheet_formatting(sheet_service, spreadsheet_id, sheet_id, header):
    """
    Applies formatting to a Google Sheet tab including:
    - Styling the header row
    - Freezing the first row
    - Setting column widths
    - Wrapping specific columns
    - Left-aligning data rows

    Parameters:
    - sheet_service: Google Sheets API service instance.
    - spreadsheet_id (str): ID of the spreadsheet.
    - sheet_id (int): ID of the sheet/tab to format.
    - header (list): List of header columns for the sheet.

    Returns:
    - None

    Exception Handling:
    - None
    """
    column_widths = [220, 250, 180, 150, 180, 150, 100, 100, 160, 160, 180, 700]
    # Applies header styling, freeze, column width, comment wrapping and alignment
    format_header_only = {
        "repeatCell": {
            "range": {
                "sheetId": sheet_id,
                "startRowIndex": 0,
                "endRowIndex": 1,
                "startColumnIndex": 0,
                "endColumnIndex": header,
            },
            "cell": {
                "userEnteredFormat": {
                    "backgroundColor": {"red": 0.2588, "green": 0.5216, "blue": 0.9569},
                    "horizontalAlignment": "LEFT",
                    "textFormat": {
                        "foregroundColor": {"red": 1.0, "green": 1.0, "blue": 1.0},
                        "bold": True,
                    },
                }
            },
            "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)",
        }
    }

    freeze_header_row = {
        "updateSheetProperties": {
            "properties": {
                "sheetId": sheet_id,
                "gridProperties": {"frozenRowCount": 1},
            },
            "fields": "gridProperties.frozenRowCount",
        }
    }

    auto_size_columns = [
        {
            "updateDimensionProperties": {
                "range": {
                    "sheetId": sheet_id,
                    "dimension": "COLUMNS",
                    "startIndex": i,
                    "endIndex": i + 1,
                },
                "properties": {"pixelSize": width},
                "fields": "pixelSize",
            }
        }
        for i, width in enumerate(column_widths)
    ]

    # Wrap text in "Comments" and "Query Name" columns
    wrap_comments_column = [
        {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": 1,
                    "startColumnIndex": 11,
                    "endColumnIndex": 12,
                },
                "cell": {"userEnteredFormat": {"wrapStrategy": "WRAP"}},
                "fields": "userEnteredFormat(wrapStrategy)",
            }
        },
        {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": 1,
                    "startColumnIndex": 1,
                    "endColumnIndex": 2,
                },
                "cell": {"userEnteredFormat": {"wrapStrategy": "WRAP"}},
                "fields": "userEnteredFormat(wrapStrategy)",
            }
        },
    ]

    align_all_left = {
        "repeatCell": {
            "range": {
                "sheetId": sheet_id,
                "startRowIndex": 1,
                "startColumnIndex": 0,
                "endColumnIndex": header,
            },
            "cell": {"userEnteredFormat": {"horizontalAlignment": "LEFT"}},
            "fields": "userEnteredFormat.horizontalAlignment",
        }
    }

    requests = [
        format_header_only,
        freeze_header_row,
        *auto_size_columns,
        *wrap_comments_column,
        align_all_left,
    ]

    sheet_service.spreadsheets().batchUpdate(
        spreadsheetId=spreadsheet_id, body={"requests": requests}
    ).execute()


@functions_framework.http
def main(_):
    """
    Main Function to generate a daily query report and send summary email.

    This function:
    - Initializes the Google Sheets API service.
    - Computes the current report time window.
    - Recreates the sheet tab for today's date.
    - Excludes queries already reported in previous tabs.
    - Fetches all active organization IDs from the platform database.
    - Aggregates and appends query data for each organization to the Google Sheet.
    - Applies formatting to the sheet tab.
    - Prepares and sends a summary email with query statistics and a link to the sheet,
      if within the allowed email window and restrictions.
    - Handles errors and logs status.

    Parameters:
    - _ (any): Dummy request object.

    Returns:
    - tuple or dict: Status message and code, or error message on failure.

    Exception Handling:
    - Logs and returns error message if any exception occurs during report generation.
    """
    try:
        # Initialize Google Sheets API service
        service = get_google_sheet_service()

        # Get current date-based time window
        start_time, end_time = compute_report_time_range()

        # Assign sheet name to current date
        sheet_tab_name = end_time.strftime("%Y-%m-%d")

        # Get all existing tabs to decide which to delete
        metadata = service.spreadsheets().get(spreadsheetId=SHEET_ID).execute()
        existing_titles = {
            s["properties"]["title"]: s["properties"]["sheetId"]
            for s in metadata["sheets"]
        }

        # Recreate sheet tab for today
        existing_titles = delete_and_recreate_sheet_tab(service, sheet_tab_name)

        # Get prior query IDs to exclude from current run
        exclude_query_ids = get_excluded_query_ids(
            service, existing_titles, sheet_tab_name
        )

        # Get active org IDs from Platform DB
        platform_client = MongoClient(PLATFORM_DB_URI)
        platform_db = platform_client[PLATFORM_DB_NAME]
        org_ids = [
            doc["_id"]
            for doc in platform_db[ORGANIZATION_DOMAIN_INFORMATION_COLLECTION].find(
                {
                    "$or": [
                        {"org_type": {"$exists": False}},
                        {"org_type": {"$ne": "dev"}},
                    ]
                },
                {"_id": 1},
            )
        ]

        # Main logic to pull all query data into the sheet
        completed, failed, pending, blocked = append_query_data_to_sheet(
            service, sheet_tab_name, org_ids, exclude_query_ids, start_time, end_time
        )

        # Prepare metadata for email
        total = completed + failed + pending + blocked
        sheet_id = existing_titles[sheet_tab_name]
        sheet_link = (
            f"https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit#gid={sheet_id}"
        )
        sheet_date_str = end_time.strftime("%m/%d/%Y")

        # Apply formatting after data insertion
        apply_sheet_formatting(service, SHEET_ID, sheet_id, header=HEADER_LENGTH)

        # Build window
        window = timedelta(minutes=int(EMAIL_WINDOW_MINUTES))

        # Determine if email should be sent
        should_send_email = True
        if RESTRICT_EMAIL_TIME and not (
            end_time - window <= get_current_datetime() <= end_time + window
        ):
            should_send_email = False

        if should_send_email:
            # Send email summary
            status_msg, status_code = send_query_summary_email(
                sheet_date_str, total, completed, pending, blocked, failed, sheet_link
            )
            logger.info(f"Email status: {status_msg} (Code: {status_code})")
            return status_msg, status_code
        else:
            logger.info(
                "Skipping email: Either RESTRICT_EMAIL_TIME is off or current time is outside "
                f"the allowed window of ±{EMAIL_WINDOW_MINUTES} mins."
            )
            return {
                "message": "Report generated. Email not sent due to time restriction."
            }

    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return {"message": "Error generating report", "error": str(e)}
