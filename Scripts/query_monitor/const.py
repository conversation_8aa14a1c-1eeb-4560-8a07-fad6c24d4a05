# Environment Variables
import os


def str_to_bool(s):
    """
    Convert a string to a boolean value.

    Accepts various representations of truthy values such as "True", "true", "1", "yes", "on".
    Returns True if the input string matches any of these (case-insensitive), otherwise False.

    Parameters:
    - s (str): The input string to convert.

    Returns:
    - bool: True if the string represents a truthy value, False otherwise.
    """
    return str(s).strip().lower() in ("True", "true", "1", "yes", "on")


ORG_URL = os.getenv("ORG_MONGO_URL")
PLATFORM_DB_URI = os.getenv("CAP_MONGO_URL")
PLATFORM_DB_NAME = os.getenv("CAP_DB_NAME")
SHEET_ID = os.getenv("SHEET_ID")
TIMEZONE = os.getenv("TIMEZONE")
HOUR = int(os.getenv("HOUR"))
MINUTE = int(os.getenv("MINUTE"))
SECOND = int(os.getenv("SECOND"))
PAST_DAYS = int(os.getenv("PAST_DAYS"))
QUERY_COLLECTION = os.getenv("QUERY_COLLECTION")
DIAGNOSTIC_COLLECTION = os.getenv("DIAGNOSTIC_COLLECTION")
ORGANIZATION_DOMAIN_INFORMATION_COLLECTION = os.getenv(
    "ORGANIZATION_DOMAIN_INFORMATION_COLLECTION"
)
ORGANIZATION_DATA_SOURCE = os.getenv("ORGANIZATION_DATA_SOURCE")
HEADER_LENGTH = os.getenv("HEADER_LENGTH")
EXCLUDE_EMAIL_PATTERN = os.getenv("EXCLUDE_EMAIL_PATTERN")
RESTRICT_EMAIL_TIME = str_to_bool(os.getenv("RESTRICT_EMAIL_TIME"))
EMAIL_WINDOW_MINUTES = os.getenv("EMAIL_WINDOW_MINUTES")

EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD")
SENDGRID_SENDER_EMAIL = os.getenv("SENDGRID_SENDER_EMAIL")
RECEIVER_EMAILS = [
    email.strip()
    for email in os.getenv("RECEIVER_EMAILS", "").split(",")
    if email.strip()
]
SENDGRID_API_KEY = os.getenv("SENDGRID_API_KEY")
CC_EMAIL = os.getenv("CC_EMAIL")
EMAIL_DISPLAY_NAME = os.getenv("EMAIL_DISPLAY_NAME")

EMAIL_BODY_TEMPLATE = """
<p>Hi All,</p>
<p>Good Day!</p>
<p>Please find the list of newly created prod queries below</p>
<p><b>Prod Queries ({sheet_date})</b><p>
<ul>
  <li>Total: {total:02}</li>
  <li>Completed: {completed:02}</li>
  <li>Ongoing: {ongoing:02}</li>
  <li>Blocked: {blocked:02}</li>
  <li>Failed: {failed:02}</li>
</ul>
<p>Link for the excel sheet: <a href="{sheet_link}">Prod queries</a></p>
<p><b>Thanks & Regards,<br>Tellagence Team<br>{sender_email}</b></p>
"""
