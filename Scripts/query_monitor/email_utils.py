from sendgrid import Send<PERSON>rid<PERSON><PERSON>lient
from sendgrid.helpers.mail import Mail, To, Email, Personalization, Cc
from utils.logger import logger
from const import (
    SENDGRID_API_KEY,
    SENDGRID_SENDER_EMAIL,
    RECEIVER_EMAILS,
    CC_EMAIL,
    EMAIL_BODY_TEMPLATE,
    EMAIL_DISPLAY_NAME,
)


def send_email(to_emails, subject, body, cc_emails=None):
    """
    Sends an email using SendGrid with support for multiple recipients and CCs.

    Parameters:
    - to_emails (str or list): Recipient email address(es).
    - subject (str): Subject of the email.
    - body (str): HTML content of the email body.
    - cc_emails (str or list, optional): CC email address(es). Default is None.

    Returns:
    - bool: True if email sent successfully, False otherwise.

    Exception Handling:
    - Catches exceptions from SendGrid and logs the error.
    """
    try:
        if not subject or not body:
            logger.warning("Email subject or body is missing. Skipping email sending.")
            return False

        message = Mail(
            from_email=Email(SENDGRID_SENDER_EMAIL, EMAIL_DISPLAY_NAME),
            subject=subject,
            html_content=body,
        )

        personalization = Personalization()

        # Add To recipients
        if isinstance(to_emails, list):
            for email in to_emails:
                personalization.add_to(To(email.strip()))
        else:
            personalization.add_to(To(to_emails.strip()))

        # Add Cc recipients
        if cc_emails:
            if isinstance(cc_emails, list):
                for cc in cc_emails:
                    personalization.add_cc(Cc(cc.strip()))
            else:
                personalization.add_cc(Cc(cc_emails.strip()))

        message.add_personalization(personalization)

        sg = SendGridAPIClient(SENDGRID_API_KEY)
        response = sg.send(message)

        logger.info(
            f"Email sent to: {', '.join(to_emails if isinstance(to_emails, list) else [to_emails])}. "
            f"Status code: {response.status_code}"
        )
        return True

    except Exception as e:
        if hasattr(e, "body"):
            logger.error(f"SendGrid error body: {e.body}")
        logger.error(f"SendGrid exception: {str(e)}")
        return False


def send_query_summary_email(
    sheet_date_str,
    total,
    completed_count,
    pending_count,
    blocked_count,
    failed_count,
    sheet_link,
):
    """
    Sends a daily summary email using SendGrid with query counts and Google Sheet link.
    Adds '[FAILED]' to the subject if any query has failed.

    Parameters:
    - sheet_date_str (str): The date of the report (e.g., "2025-07-11").
    - total (int): Total number of queries for the day.
    - completed_count (int): Number of completed queries.
    - pending_count (int): Number of ongoing or pending queries.
    - blocked_count (int): Number of blocked queries.
    - failed_count (int): Number of failed queries.
    - sheet_link (str): URL to the public Google Sheet report.

    Returns:
    - tuple: (status_message: str, status_code: int)
        - status_message: Indicates whether the email was sent or failed.
        - status_code: HTTP-like code for success (200) or internal failure (500).

    Exception Handling:
    - Catches exceptions while sending email and logs the error.
    """
    if failed_count > 0:
        subject = f"Action Required: Discover | Prod Queries Failed - {sheet_date_str}"
    else:
        subject = f"Discover | Prod Queries - {sheet_date_str}"

    email_body = EMAIL_BODY_TEMPLATE.format(
        sheet_date=sheet_date_str,
        total=total,
        completed=completed_count,
        ongoing=pending_count,
        blocked=blocked_count,
        failed=failed_count,
        sheet_link=sheet_link,
        sender_email=SENDGRID_SENDER_EMAIL,
    )

    try:
        success = send_email(RECEIVER_EMAILS, subject, email_body, CC_EMAIL)
        if not success:
            return "Failed to send email.", 200
        return "Email sent successfully via SendGrid.", 200
    except Exception as e:
        logger.error(f"Failed to send summary email via SendGrid: {str(e)}")
        return f"Failed to send email: {e}", 500
