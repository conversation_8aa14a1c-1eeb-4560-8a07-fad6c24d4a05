import os
from dotenv import load_dotenv

load_dotenv()


PROD_PLATFORM_MONGO_URL = os.getenv("PROD_PLATFORM_MONGO_URL")
RC_PLATFORM_MONGO_URL = os.getenv("RC_PLATFORM_MONGO_URL")
PROD_PLATFORM_DB_NAME = os.getenv("PROD_PLATFORM_DB_NAME")
RC_PLATFORM_DB_NAME = os.getenv("RC_PLATFORM_DB_NAME")
CLIENT_API_MONGODB_URL = os.getenv("CLIENT_API_MONGODB_URL")
DEV_ORG_MONGO_URL = os.getenv("DEV_ORG_MONGO_URL")

RC_VAULT_DB_NAME = os.getenv("RC_VAULT_DB_NAME")
PROD_VAULT_DB_NAME = os.getenv("PROD_VAULT_DB_NAME")

PROD_ORG_MONGO_URL = os.getenv("PROD_ORG_MONGO_URL")
RC_ORG_MONGO_URL = os.getenv("RC_ORG_MONGO_URL")

USER_INFORMATION_COLLECTION = os.getenv("USER_INFORMATION_COLLECTION")
ORGANIZATION_USERS_COLLECTION = os.getenv("ORGANIZATION_USERS_COLLECTION")
ORGANIZATION_DOMAIN_INFORMATION_COLLECTION = os.getenv(
    "ORGANIZATION_DOMAIN_INFORMATION_COLLECTION"
)
QUERY_COLLECTION = os.getenv("QUERY_COLLECTION")

DUMP_BASE_PATH = os.getenv("DUMP_BASE_PATH")
PLATFORM_DUMP_DIRECTORY_NAME = os.getenv("PLATFORM_DUMP_DIRECTORY_NAME")
VAULT_DUMP_DIRECTORY_NAME = os.getenv("VAULT_DUMP_DIRECTORY_NAME")

EXCLUDE_EMAIL_SUBSTRINGS = os.getenv("EXCLUDE_EMAIL_SUBSTRINGS", "").split(",")

# Parameters for migration script

# ORG_IDS: Comma-separated string of organization IDs to process during migration.
# Example: "66b62f787d3eda5b857bb329,66bb463ea948d0caa80f1281"
ORG_IDS = []

# Dev test user parameters
EMAIL_TEMPLATE = "prathap.sainadh+{counter}@tudip.com"
FIRST_NAME_TEMPLATE = "prathap+{counter}"
LAST_NAME_TEMPLATE = "sainadh+{counter}"
USERNAME_TEMPLATE = "{org}_Test{counter}"

HYDRATION_BUCKET_NAME_PREFIX = "hydration_bucket_rc_"
HYDRATION_TEMP_BUCKET_NAME_PREFIX = "hydration_bucket_temp_rc_"
QUERY_ARCHIVE_BUCKET_NAME_PREFIX = "query_archive_bucket_rc_"
DATA_EXPORT_BUCKET_NAME_PREFIX = "data_export_bucket_rc_"
