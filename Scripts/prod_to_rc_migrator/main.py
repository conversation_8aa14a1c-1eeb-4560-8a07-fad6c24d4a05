import os
from bson import ObjectId
import subprocess
from utils.logger import logger
from pymongo import MongoClient
from const import (
    PROD_PLATFORM_MONGO_URL,
    RC_PLATFORM_MONGO_URL,
    PROD_PLATFORM_DB_NAME,
    RC_PLATFORM_DB_NAME,
    PROD_ORG_MONGO_URL,
    RC_ORG_MONGO_URL,
    DUMP_BASE_PATH,
    EXCLUDE_EMAIL_SUBSTRINGS,
    EMAIL_TEMPLATE,
    FIRST_NAME_TEMPLATE,
    LAST_NAME_TEMPLATE,
    USERNAME_TEMPLATE,
    ORG_IDS,
    PROD_VAULT_DB_NAME,
    RC_VAULT_DB_NAME,
    HYDRATION_BUCKET_NAME_PREFIX,
    DATA_EXPORT_BUCKET_NAME_PREFIX,
    HYDRATION_TEMP_BUCKET_NAME_PREFIX,
    QUERY_ARCHIVE_BUCKET_NAME_PREFIX,
    DEV_ORG_MONGO_URL,
    CLIENT_API_MONGODB_URL,
    USER_INFORMATION_COLLECTION,
    ORGANIZATION_DOMAIN_INFORMATION_COLLECTION,
    ORGANIZATION_USERS_COLLECTION,
    QUERY_COLLECTION,
)


def run_command(cmd: list):
    """
    Run a shell command and raise an error if it fails.

    Parameters:
    - cmd (list): List of command arguments to execute.

    Returns:
    - None

    Exception Handling:
    - None
    """
    subprocess.run(cmd, check=True)


def migrate_discover_platform(organization_ids):
    """
    Migrate Discover Platform and Vault DBs with filters:
    - For `organization users` and `organization domain information`, migrate only docs matching given organization_ids.
    - Use bulk insertion for faster migration.

    Parameters:
    - organization_ids (list[str] or list[ObjectId]): List of organization ObjectIds.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Convert all organization_ids to ObjectId if they aren't already
    org_ids = [
        ObjectId(org_id) if not isinstance(org_id, ObjectId) else org_id
        for org_id in organization_ids
    ]

    # Initialize source (prod) and destination (RC) MongoDB clients
    prod_platform_client = MongoClient(PROD_PLATFORM_MONGO_URL)
    rc_platform_client = MongoClient(RC_PLATFORM_MONGO_URL)

    # Connect to Discover platform DBs
    prod_platform_db = prod_platform_client[PROD_PLATFORM_DB_NAME]
    rc_platform_db = rc_platform_client[RC_PLATFORM_DB_NAME]

    # Connect to Vault DBs
    prod_platform_vault_db = prod_platform_client[PROD_VAULT_DB_NAME]
    rc_platform_vault_db = rc_platform_client[RC_VAULT_DB_NAME]

    # Define filters only for selective collections (others will migrate fully)
    def get_filter(platform_collection_name):
        if platform_collection_name == ORGANIZATION_USERS_COLLECTION:
            return {"organization_id": {"$in": org_ids}}
        elif platform_collection_name == ORGANIZATION_DOMAIN_INFORMATION_COLLECTION:
            return {"_id": {"$in": org_ids}}
        elif platform_collection_name == USER_INFORMATION_COLLECTION:
            # Step 1: Fetch user_ids from organization_users for given org_ids
            user_ids = list(
                prod_platform_db[ORGANIZATION_USERS_COLLECTION]
                .find({"organization_id": {"$in": org_ids}}, {"user_id": 1})
                .distinct("user_id")
            )
            # Step 2: Filter user_information by those _ids
            return {"_id": {"$in": user_ids}}
        return {}  # No filter for other collections

    # Migrate a single collection using bulk insert with deduplication
    def migrate_collection(prod_coll, rc_coll, coll_name, batch_size=1000):
        query = get_filter(coll_name)
        inserted_count = 0
        buffer = []

        # Fetch existing _ids in the destination collection for deduplication
        existing_ids = set(doc["_id"] for doc in rc_coll.find(query, {"_id": 1}))

        for doc in prod_coll.find(query):
            if doc["_id"] not in existing_ids:
                buffer.append(doc)
                if len(buffer) >= batch_size:
                    rc_coll.insert_many(buffer)
                    inserted_count += len(buffer)
                    buffer.clear()

        # Insert remaining docs
        if buffer:
            rc_coll.insert_many(buffer)
            inserted_count += len(buffer)

        return inserted_count

    # Migrate all collections in a given database
    def migrate_database(prod_db, rc_db, db_name):
        print(f"\nMigrating: {db_name}")

        # Convert ObjectIds to strings for substring checks
        org_id_strs = [str(oid) for oid in org_ids]

        for coll_name in prod_db.list_collection_names():
            # Migrate only if any org_id string is part of the collection name for organization vault collections
            if (
                not any(org_id_str in coll_name for org_id_str in org_id_strs)
                and db_name == PROD_VAULT_DB_NAME
            ):
                continue

            print(f"Migrating collection: {coll_name}")

            prod_coll = prod_db[coll_name]
            rc_coll = rc_db[coll_name]

            count = migrate_collection(prod_coll, rc_coll, coll_name)
            print(f"Inserted {count} docs into {coll_name}")

    # Start migration for Discover platform database
    migrate_database(prod_platform_db, rc_platform_db, PROD_PLATFORM_DB_NAME)

    # Start migration for Vault database
    migrate_database(prod_platform_vault_db, rc_platform_vault_db, PROD_VAULT_DB_NAME)

    print("\nMigration completed successfully.")

    # Transform Discover platform users post-migration
    transform_discover_users(organization_ids=organization_ids)


def transform_discover_users(organization_ids):
    """
    Transform user_information collection in RC Discover DB to anonymize users.

    Parameters:
    - organization_ids (list[str]): List of organization IDs to process.

    Returns:
    - None

    Exception Handling:
    - Skips users with excluded email domains or no organization mapping.
    """
    logger.info("Transforming user_information in RC Discover DB")

    client = MongoClient(RC_PLATFORM_MONGO_URL)
    db = client[RC_PLATFORM_DB_NAME]

    user_coll = db[USER_INFORMATION_COLLECTION]
    org_users_coll = db[ORGANIZATION_USERS_COLLECTION]
    org_domain_info_coll = db[ORGANIZATION_DOMAIN_INFORMATION_COLLECTION]

    # Create a mapping of org_id to org_name
    org_map = {str(org["_id"]): org["name"] for org in org_domain_info_coll.find()}
    # Map each user_id to a set of organizations they belong to
    org_user_map = {}
    for doc in org_users_coll.find():
        uid = str(doc.get("user_id"))
        oid = str(doc.get("organization_id"))
        org_user_map.setdefault(uid, set()).add(org_map.get(oid))

    counter = 10  # Start index for generating fake user data

    for user in user_coll.find():
        email = user.get("email", "")
        user_id = str(user["_id"])

        # Skip excluded emails (internal/service/etc.)
        if any(exclude_email in email for exclude_email in EXCLUDE_EMAIL_SUBSTRINGS):
            continue

        # Skip users not belonging to any known org
        orgs = org_user_map.get(user_id)
        if not orgs:
            continue
        # Pick any one org name to embed in username
        org_name = next(iter(orgs))
        # Update anonymized fields
        user_coll.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "email": EMAIL_TEMPLATE.format(counter=counter),
                    "first_name": FIRST_NAME_TEMPLATE.format(counter=counter),
                    "last_name": LAST_NAME_TEMPLATE.format(counter=counter),
                    "username": USERNAME_TEMPLATE.format(org=org_name, counter=counter),
                }
            },
        )
        counter += 1

    org_object_ids = list(map(ObjectId, organization_ids))

    # Clean up unrelated org-user mappings
    org_users_coll.delete_many({"organization_id": {"$nin": org_object_ids}})

    logger.info("Finished user transformation.")

    logger.info("Initiating organization data transformation")

    # Initiate organization vault transformation for these orgs
    transform_organization_vault_data(organization_ids)


def transform_organization_vault_data(organization_ids):
    """
    Transforms all organization vault collections.

    This function performs the following:
    - Updates each organization's account_information collection with standard bucket names,
      URLs, and MongoDB connection strings.

    Parameters:
    - organization_ids (List[str]): A list of organization IDs (as strings) whose data should be retained and enriched.

    Returns:
    - None

    Exception Handling:
    - None
    """
    client = MongoClient(RC_PLATFORM_MONGO_URL)
    rc_vault_db = client[RC_VAULT_DB_NAME]

    # Enrich vault account_information collections with bucket names and URLs
    for org_id in organization_ids:
        vault_collection = rc_vault_db[f"{org_id}_account_information"]
        vault_collection.update_one(
            {"organization_id": ObjectId(org_id)},
            [
                {
                    "$set": {
                        "hydration_bucket_name": {
                            "$concat": [HYDRATION_BUCKET_NAME_PREFIX, org_id]
                        },
                        "hydration_temp_bucket_name": {
                            "$concat": [HYDRATION_TEMP_BUCKET_NAME_PREFIX, org_id]
                        },
                        "query_archive_bucket_name": {
                            "$concat": [QUERY_ARCHIVE_BUCKET_NAME_PREFIX, org_id]
                        },
                        "data_export_bucket_name": {
                            "$concat": [DATA_EXPORT_BUCKET_NAME_PREFIX, org_id]
                        },
                        "mongodb_url": DEV_ORG_MONGO_URL,
                        "client_api_mongodb_url": CLIENT_API_MONGODB_URL,
                    }
                }
            ],
        )
    logger.info("Finished organization data transformation.")


def migrate_all_orgs():
    """
    Dump, restore, and transform all organization databases.

    Parameters:
    - None

    Returns:
    - None

    Exception Handling:
    - Logs and skips any organizations that fail to migrate or transform.
    """
    client = MongoClient(RC_PLATFORM_MONGO_URL)
    org_vault = client[RC_VAULT_DB_NAME]
    rc_user_map = load_discover_users(client[RC_PLATFORM_DB_NAME])

    # Fetch all organization db name from vault db and send for migration and transformation
    for coll_name in org_vault.list_collection_names():
        doc = org_vault[coll_name].find_one()
        org_db = doc.get("organization_db_name") if doc else None
        if not org_db:
            continue
        try:
            migrate_and_transform_org(org_db, rc_user_map)
        except Exception as e:
            logger.error(f"Failed to migrate {org_db}: {e}")


def load_discover_users(discover_db):
    """
    Load all users from RC Discover DB and return a user_id to metadata mapping.

    Parameters:
    - discover_db (Database): MongoDB database object for Discover RC DB.

    Returns:
    - dict: Mapping of user_id to dict with email, first_name, last_name.

    Exception Handling:
    - None
    """
    user_coll = discover_db[USER_INFORMATION_COLLECTION]
    return {
        str(user["_id"]): {
            "email": user.get("email"),
            "first_name": user.get("first_name"),
            "last_name": user.get("last_name"),
        }
        for user in user_coll.find()
    }


def migrate_and_transform_org(org_db_name, rc_user_map):
    """
    Dump, restore, and transform the given organization database.

    Parameters:
    - org_db_name (str): Name of the organization database to migrate.
    - rc_user_map (dict): Map of user_id to user metadata for enrichment.

    Returns:
    - None

    Exception Handling:
    - Skips queries with unmapped user_ids.
    - Raises errors on dump or restore failure.
    """
    logger.info(f"Migrating ORG DB: {org_db_name}")
    config_param = "?readPreference=primaryPreferred&socketTimeoutMS=90000000&connectTimeoutMS=60000000"
    dump_path = os.path.join(DUMP_BASE_PATH, org_db_name)

    # Dump org DB from production
    run_command(
        [
            "mongodump",
            "--uri",
            f"{PROD_ORG_MONGO_URL}{org_db_name}{config_param}",
            "--gzip",
            "--out",
            dump_path,
        ]
    )

    run_command(
        [
            "mongorestore",
            "--uri",
            RC_ORG_MONGO_URL,
            "--nsInclude",
            f"{org_db_name}.*",
            "--gzip",
            "--numInsertionWorkersPerCollection",
            "4",
            "--numParallelCollections",
            "4",
            dump_path,
        ]
    )

    logger.info(f"Restored ORG DB: {org_db_name}")
    org_db = MongoClient(RC_ORG_MONGO_URL)[org_db_name]

    # Inject transformed user metadata into query collection
    if QUERY_COLLECTION in org_db.list_collection_names():
        query_coll = org_db[QUERY_COLLECTION]
        query_counter = 0
        for query in enumerate(query_coll.find({}, {"_id": 1, "user_id": 1}), 1):
            query_counter += 1
            user_data = rc_user_map.get(str(query.get("user_id")))
            if user_data:
                query_coll.update_one(
                    {"_id": query["_id"]},
                    {
                        "$set": {
                            "meta_data.USER_EMAIL": user_data["email"],
                            "meta_data.USER_FIRST_NAME": user_data["first_name"],
                            "meta_data.USER_LAST_NAME": user_data["last_name"],
                        }
                    },
                )

    logger.info(f"Transformed {query_counter} queries in {org_db_name}")


if __name__ == "__main__":
    """
    Entry point for the script.

    Executes:
    - Platform migration (Discover DB)
    - All organization DB migrations

    Returns:
    - None

    Exception Handling:
    - None
    """
    migrate_discover_platform(organization_ids=ORG_IDS)
    migrate_all_orgs()

    logger.info("All DBs migrated and transformed.")
