# """
# This module contains common methods used in different consumers.
# """

# from bson import ObjectId
# from google.api_core.exceptions import Google<PERSON>IError
# from google.cloud import storage
# from utils.const import PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID
# from utils.utilities import OrganizationAccountInfo, MetaContainer
# from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
# from utils.pubsub_publisher import publish_pubsub_message
# from utils.utilities import CONSUMER_TYPE
# from utils.logger import logger


# class CapabilitiesUtils:
#     """
#     A utility class for managing capabilities related to an organization, including
#     MongoDB configuration, data fetching, file storage, and message publishing.
#     """

#     def __init__(self):
#         self.organization_info = None
#         self.payload = None
#         self.query = None
#         self.meta_container = None
#         self.rac_collection = None
#         self.mongo_client = None
#         self.organization_db = None

#     def set_configuration(self, payload):
#         """
#         Sets the configuration for the utility based on the provided payload.

#         Parameters:
#         - payload (dict): A dictionary containing configuration details, including
#           the organization ID and query ID.

#         Returns:
#         - self: The CapabilitiesUtils instance with updated configuration.

#         Exception Handling:
#         - None
#         """
#         self.payload = payload
#         self.organization_info = OrganizationAccountInfo(payload["organization_id"])


#         client = get_mongodb_client(self.organization_info.mongodb_url)
#         self.mongo_client = client

#         if client is not None:
#             # with client:
#             db_name = self.organization_info.organization_db_name
#             db = get_mongodb_db(client, db_name)
#             self.organization_db = db
#             self.query = get_mongodb_collection(db, "query").find_one(
#                 {"_id": ObjectId(payload["query_id"])}
#             )
#             self.meta_container = MetaContainer()

#             if self.query is not None:
#                 self.meta_container.set_meta_data(self.query["meta_data"])
#                 self.meta_container.set_payload_info(payload)

#         return self

#     def fetch_data(self):
#         """
#         Fetches data from the MongoDB collection based on the current configuration.

#         This method connects to the MongoDB database using the organization's MongoDB URL,
#         selects the appropriate database, and retrieves documents from a specified collection
#         based on the encapsulation marker provided in the payload.

#         Parameters:
#         - self (CapabilitiesUtils): The instance of the CapabilitiesUtils class.

#         Returns:
#         - pymongo.cursor.Cursor: A cursor to the documents matching the query criteria.

#         Exception Handling:
#         - None
#         """
#         if self.organization_info is not None:
#             client = get_mongodb_client(self.organization_info.mongodb_url)

#             if client is not None:
#                 db_name = self.organization_info.organization_db_name
#                 db = get_mongodb_db(client, db_name)

#                 if (
#                     self.query
#                     and self.payload
#                     and self.meta_container
#                     and self.meta_container.meta_data
#                 ):
#                     collection_name = self.meta_container.meta_data[
#                         "RAC_COLLECTION_NAME"
#                     ]
#                     collection = get_mongodb_collection(db, collection_name)
#                     self.rac_collection = collection

#                     return collection.find(
#                         {
#                             "encapsulation_marker": self.payload[
#                                 "encapsulation_marker"
#                             ],
#                         },
#                         {"id", "date", "BODY1", "Lemitized", "Keyword", "Hashtag"},
#                     )

#     def store_file(self, blob_name, file_content) -> None:
#         """
#         Write content to a file in a Google Cloud Storage bucket.

#         Parameters:
#         - blob_name: The name of the blob (file) in the bucket.
#         - content: The content to write to the file.

#         Returns:
#         - None

#         Exception Handling:
#         - GoogleAPIError: If there is an error while interacting with the Google Cloud Storage API.
#         - Exception: If an unexpected error occurs while storing the file.
#         """
#         try:
#             # Initialize a storage client
#             storage_client = storage.Client()

#             if self.organization_info:
#                 bucket_name = self.organization_info.result_storage_bucket_name
#                 # Get the bucket
#                 bucket = storage_client.bucket(bucket_name)

#                 # Create a new blob in the bucket
#                 blob = bucket.blob(blob_name)

#                 # Write content to the blob
#                 blob.upload_from_string(file_content)

#                 logger.info("File %s uploaded to %s.", blob_name, bucket_name)

#         except GoogleAPIError as e:
#             logger.info("Failed to write to bucket: %s", e)

#         except Exception as e:
#             logger.info("An unexpected error occurred while storing the file: %s", e)

#     def publish_message(self):
#         """
#         Publishes a message to a Google Cloud Pub/Sub topic.

#         Parameters:
#         - None

#         Returns:
#         - None

#         Exception Handling:
#         - None
#         """
#         self.payload["publisher_type"] = CONSUMER_TYPE
#         publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, self.payload)

import os
from bson import ObjectId
from utils.utilities import OrganizationAccountInfo, MetaContainer
from utils.pubsub_publisher import publish_pubsub_message
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from google.api_core.exceptions import GoogleAPIError
from google.cloud import storage
from utils.const import PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, QUERY_COLLECTION
from utils.utilities import OrganizationAccountInfo, MetaContainer, RacTransform
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import CONSUMER_TYPE
from utils.logger import logger


class CapabilitiesUtils:
    """
    A utility class for managing capabilities related to an organization, including
    MongoDB configuration, data fetching, file storage, and message publishing.
    """

    def __init__(self):
        self.organization_info = None
        self.payload = None
        self.query = None
        self.meta_container = None
        self.rac_collection = None
        self.mongo_client = None
        self.organization_db = None

    def set_configuration(self, payload):
        """
        Sets the configuration for the utility based on the provided payload.

        Parameters:
        - payload (dict): A dictionary containing configuration details, including
          the organization ID and query ID.

        Returns:
        - self: The CapabilitiesUtils instance with updated configuration.

        Exception Handling:
        - None
        """
        self.payload = payload
        self.organization_info = OrganizationAccountInfo(payload["organization_id"])
        client = get_mongodb_client(self.organization_info.mongodb_url)
        self.mongo_client = client

        if client is not None:
            # with client:
            db_name = self.organization_info.organization_db_name
            db = get_mongodb_db(client, db_name)
            self.organization_db = db
            self.query = get_mongodb_collection(db, QUERY_COLLECTION).find_one(
                {"_id": ObjectId(payload["query_id"])}
            )
            self.meta_container = MetaContainer()
            self.meta_container.set_payload_info(payload)

            if self.query is not None:
                self.meta_container.set_meta_data(self.query["meta_data"])
                collection_name = self.meta_container.meta_data["RAC_COLLECTION_NAME"]
                collection = get_mongodb_collection(db, collection_name)
                self.rac_collection = collection

        return self

    def fetch_data(self):
        """
        Fetches data from the MongoDB collection based on the current configuration.

        This method connects to the MongoDB database using the organization's MongoDB URL,
        selects the appropriate database, and retrieves documents from a specified collection
        based on the encapsulation marker provided in the payload.

        Parameters:
        - self (CapabilitiesUtils): The instance of the CapabilitiesUtils class.

        Returns:
        - pymongo.cursor.Cursor: A cursor to the documents matching the query criteria.

        Exception Handling:
        - None
        """
        if self.organization_info is not None:
            client = get_mongodb_client(self.organization_info.mongodb_url)
            if client is not None:
                db_name = self.organization_info.organization_db_name
                db = get_mongodb_db(client, db_name)
                if (
                    self.query
                    and self.payload
                    and self.meta_container
                    and self.meta_container.meta_data
                ):
                    collection = self.rac_collection
                    return collection.find(
                        {
                            "encapsulation_marker": self.payload[
                                "encapsulation_marker"
                            ],
                        },
                        {RacTransform.Tellagence_ID.value, RacTransform.Tellagence_Date.value, RacTransform.BODY1.value, RacTransform.Lemitized.value, RacTransform.Keyword.value, RacTransform.Hashtag.value},
                    )

    def store_file(self, blob_name, file_content):
        """
        Write content to a file in a Google Cloud Storage bucket.

        Parameters:
        - blob_name: The name of the blob (file) in the bucket.
        - content: The content to write to the file.

        Returns:
        - None

        Exception Handling:
        - GoogleAPIError: If there is an error while interacting with the Google Cloud Storage API.
        - Exception: If an unexpected error occurs while storing the file.
        """
        try:
            # Initialize a storage client
            storage_client = storage.Client()
            if self.organization_info:
                bucket_name = self.organization_info.result_storage_bucket_name
                # Get the bucket
                bucket = storage_client.bucket(bucket_name)

                # Create a new blob in the bucket
                blob = bucket.blob(blob_name)

                # Write content to the blob
                blob.upload_from_string(file_content)

                logger.info(f"File {blob_name} uploaded to {bucket_name}.")

        except GoogleAPIError as e:
            logger.info(f"Failed to write to bucket: {e}")
        except Exception as e:
            logger.info(f"An unexpected error occurred while storing the file: {e}")

    def publish_message(self):
        """
        Publishes a message to a Google Cloud Pub/Sub topic.

        Parameters:
        - None

        Returns:
        - None

        Exception Handling:
        - None
        """
        self.payload["publisher_type"] = CONSUMER_TYPE
        publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, self.payload)
