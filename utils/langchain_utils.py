"""
Module to define langchain utils
"""

from typing import List
import backoff
from google.api_core import exceptions
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_google_vertexai import VertexAIEmbeddings
from langchain_openai import ChatOpenAI
import numpy as np
from utils.llm_settings import (
    VERTEX_EMBEDDING_MAX_TOKEN_COUNT,
    VERTEX_EMBEDDING_MODELS,
    GEMINI_MODELS,
    GEMINI_MAX_TOKEN_COUNT,
    GPT_MAX_TOKEN_COUNT,
    GPT_MODELS,
    MAX_RETRIES,
    RESOURCE_EXHAUST_RETRY_TIME,
    SERVER_ERRORS_RETRY_TIME,
    gemini_safety_parameters,
)
from utils.utilities import CONSUMER_TYPE, PublisherType
from utils.logger import logger


class LangChainUtility:
    """Class to define langcahain utils"""

    def __init__(self):
        self.model_name = None
        self.embedding_model_name = None
        self.llm_model = None
        self.embedding_model = None
        self.api_keys = None
        self.max_token_count = None
        self.embedding_max_token_count = None
        self.chain = None
        self.consumer_type = None

    def get_num_tokens(self, docs):
        """Returns the number of tokens in a list of documents"""
        return self.llm_model.get_num_tokens("\n".join(docs))

    def split_with_tokens(self, docs):
        "Split the list of texts (docs) and returns batches of the texts"
        token_count = self.get_num_tokens(docs)

        # Calculate the number of the split from token count and or the maximum number of the texts per batch
        n = token_count // self.max_token_count + 1

        # create the batches for embedding
        batches = np.array_split(docs, n)

        return batches

    def split_with_characters(self, docs: np.ndarray, max_char_count=15000):
        """
        Split the documents array into batches based on the maximum character count.

        Parameters:
        - docs: np.ndarray of documents (texts)
        - max_char_count: Maximum allowed characters per batch

        Returns:
        - List of document batches
        """
        # Calculate the length of each document (number of characters)
        doc_lengths = np.vectorize(len)(docs)

        # Create an array to hold the indices where splits will occur
        split_indices = []
        current_batch_size = 0

        for i, length in enumerate(doc_lengths):
            # Add the current document length to the batch size
            current_batch_size += length

            # If adding this document exceeds the max_char_count, split here
            if current_batch_size > max_char_count:
                split_indices.append(i)
                current_batch_size = (
                    length  # Reset for the next batch starting with this doc
                )

        # Split the docs array at the calculated indices
        batches = np.split(docs, split_indices)
        return batches

    def get_llm(self):
        """
        Method to get the llm object with different API keys

        Returns:
        - llm : llm model object with fallback
        """
        model = self.model_name
        api_keys = self.api_keys
        consumer_type = self.consumer_type
        llm_list = []
        fallback_llms = []
        if api_keys:
            for api_key in api_keys:
                if model in GEMINI_MODELS:
                    llm_list.append(
                        ChatGoogleGenerativeAI(
                            model=model,
                            google_api_key=api_key,
                            safety_settings=gemini_safety_parameters,
                            max_retries=MAX_RETRIES,
                            temperature=(
                                0.1
                                if consumer_type
                                in {
                                    PublisherType.CATEGORIZATION_TOOL.value,
                                    PublisherType.SENTIMENT.value,
                                    PublisherType.SENTIMENT_MULTI.value,
                                }
                                else 0.7
                            ),
                            top_p=(
                                1
                                if consumer_type
                                in {
                                    PublisherType.CATEGORIZATION_TOOL.value,
                                }
                                else None
                            ),
                        )
                    )
                elif model in GPT_MODELS:
                    llm_list.append(
                        ChatOpenAI(
                            temperature=0, model_name=model, openai_api_key=api_key
                        )
                    )
                else:
                    raise ValueError("Wrong llm type specified")
        else:
            raise ValueError("No API keys for specified LLM")

        # pick the first llm from the list
        llm = llm_list[0]
        fallback_llms.extend(llm_list[1:])
        return llm.with_fallbacks(fallbacks=fallback_llms)

    def get_embedding_model(self):
        """
        Method to get the embedding model object

        Returns:
        - embedding_model : Embedding model object

        Exception Handling:
        - None
        """
        model_name = self.embedding_model_name
        embedding_model = None

        if model_name in VERTEX_EMBEDDING_MODELS:
            embedding_model = VertexAIEmbeddings(model_name=model_name)
        else:
            raise ValueError(f"Invalid embedding model specified: {embedding_model}")

        return embedding_model

    def get_max_token_count(self):
        """
        Sets the maximum token count for the model and embedding model.

        Exception Handling:
        - ValueError: Raises If the specified LLM model name or embedding model name is not supported.
        """
        # Set max token count for the LLM model
        model = self.model_name
        if model in GEMINI_MODELS:
            self.max_token_count = GEMINI_MAX_TOKEN_COUNT
        elif model in GPT_MODELS:
            self.max_token_count = GPT_MAX_TOKEN_COUNT
        else:
            raise ValueError(f"Invalid LLM model specified: {model}")

        # Set max token count for the embedding model
        embedding_model = self.embedding_model_name
        if embedding_model in VERTEX_EMBEDDING_MODELS:
            self.embedding_max_token_count = VERTEX_EMBEDDING_MAX_TOKEN_COUNT
        elif embedding_model is not None:
            raise ValueError(f"Invalid embedding model specified: {embedding_model}")

    def backoff_for_resource_exhaustion():
        """
        A backoff decorator that retries the function execution using an
        exponential backoff strategy when a ResourceExhausted (error code 429)
        exception occurs.

        Returns:
        - Decorator for retrying on ResourceExhausted errors.

        Exception Handling:
        - None
        """
        return backoff.on_exception(
            backoff.expo,
            exceptions.ResourceExhausted,  # Retry for GRPC error code 429
            max_time=int(RESOURCE_EXHAUST_RETRY_TIME),
        )

    def backoff_for_server_errors():
        """
        A backoff decorator that retries the function execution using an
        exponential backoff strategy when any of the following exceptions occur:
        InternalServerError, FailedPrecondition, or ServiceUnavailable.

        Returns:
        - Decorator for retrying on server-related errors.

        Exception Handling:
        - None
        """
        return backoff.on_exception(
            backoff.expo,
            (
                exceptions.InternalServerError,
                exceptions.FailedPrecondition,
                exceptions.ServiceUnavailable,  # Retry for GRPC server errors
            ),
            max_time=int(SERVER_ERRORS_RETRY_TIME),
        )

    @backoff_for_resource_exhaustion()
    @backoff_for_server_errors()
    def invoke_llm(self, prompt: str):
        """
        Invoke the LLM model with the provided prompt.

        Parameters:
        - prompt (str): The prompt string to send to the LLM model.

        Returns:
        - The response from the LLM model.

        Exception Handling:
        - None
        """
        model_response = self.llm_model.invoke(prompt)
        return model_response

    @backoff_for_resource_exhaustion()
    def invoke_chain(self, docs: str):
        """
        Invoke the LLM model with the provided prompt.

        Parameters:
        - prompt (str): The prompt string to send to the LLM model.

        Returns:
        - The response from the LLM model.
        """
        chain_response = self.chain.invoke(docs)
        return chain_response

    @backoff_for_resource_exhaustion()
    @backoff_for_server_errors()
    def embed_texts(self, texts: List[str], task_type: str):
        """
        Invoke embedding model with the provided texts and task type.

        Parameters:
        - texts (List[str]): A list of strings for which embeddings need to be generated.
        - task_type (str): Specifies the type of task used for generating embeddings, such as "classification," "clustering," or "recommendation."

        Returns:
        - The response from the embedding model.

        Exception Handling:
        - None
        """
        model_response = self.embedding_model.embed(
            texts=texts, embeddings_task_type=task_type
        )
        return model_response

    def set_configuration(
        self, model_name, collection, embedding_model_name=None, consumer_type=None
    ):
        """
        Sets different configuration for the langchain like
        - model_name: Name of the model for summarization
        - api_keys: API keys for the LLMs
        - max_token_count: get max token for the given model
        - llm_model: llm model object with fallback.
        - embedding_model: embedding model object.

        Parameters:
        - model_name: model name for summarization
        - collection: Mongo DB collection
        - embedding_model_name: model name for embedding

        Returns:
        - None
        """
        self.model_name = model_name
        self.embedding_model_name = embedding_model_name

        self.consumer_type = consumer_type
        if self.consumer_type is None:
            self.consumer_type = CONSUMER_TYPE

        logger.info(
            "self.consumer type is %s and consumer_type is %s",
            self.consumer_type,
            consumer_type,
        )
        logger.info(
            "self.model name is %s and model name is %s", self.model_name, model_name
        )

        self.api_keys = collection.distinct(
            "API_KEY", {"CONSUMER_TYPE": self.consumer_type, "MODEL_NAME": model_name}
        )
        self.get_max_token_count()
        self.llm_model = self.get_llm()
        if embedding_model_name:
            self.embedding_model = self.get_embedding_model()
            