"""
Module to have all constants and env imports.
"""

import os

PROJECT_ID = os.getenv("PROJECT_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
CAPABILITIES_MONGO_URL = os.getenv("CAPABILITIES_MONGO_URL")
CAPABILITIES_DB_NAME = os.getenv("CAPABILITIES_DB_NAME")
ERROR_LOG_COLLECTION = os.getenv("ERROR_LOG_COLLECTION")
ERROR_CONFIG = os.getenv("ERROR_CONFIG")
FILE_EXTENSIONS = [".csv", ".txt", ".json", ".xlsx", ".html", "xml", "yaml"]
DELIMETERS = [",", "\t", "|", " ", ";"]

ORGANIZATION_VAULT_BASE_URL = os.getenv("ORGANIZATION_VAULT_BASE_URL")
ORGANIZATION_VAULT_ORG_ENDPOINT = os.getenv("ORGANIZATION_VAULT_ORG_ENDPOINT")
VAULT_API_BASE_DELAY = os.getenv("VAULT_API_BASE_DELAY")
VAULT_API_MAX_RETRIES = os.getenv("VAULT_API_MAX_RETRIES")
PROJECT_ID = os.getenv("PROJECT_ID")
DIAGNOSTIC_TOPIC_ID = os.getenv("DIAGNOSTIC_TOPIC_ID")
CONSUMER_TYPE = os.getenv("CONSUMER_TYPE")
QUERY_COLLECTION = os.getenv("QUERY_COLLECTION")

DEFAULT_EXPLANATION = "No explanation generated"
DEFAULT_SENTIMENT = "No sentiment generated"
SENTIMENT_CLASSIFICATION = ["Negative", "Neutral", "Positive"]
