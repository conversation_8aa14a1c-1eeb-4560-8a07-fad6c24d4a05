"""
This module defines a Cloud Function that processes incoming CloudEvents, performs a series
of operations based on the payload, and interacts with various systems to manage the flow
of data across different stages.

The function follows a series of steps based on the publisher type and updates various markers,
triggers specific topics, and ensures the correct data flow throughout the system. It also
manages the metadata and access to MongoDB collections, updating relevant fields and publishing
messages to different topics based on the processing status.
"""

from typing import List, Optional
from datetime import datetime, timezone
import json
import base64
from bson import ObjectId
from pymongo.collection import Collection
import functions_framework
from google.cloud import bigquery
from cloudevents.http import CloudEvent
from const import (
    CATEGORIZATION_INDEXES,
    QUERY_STATUS_METADATA_KEY,
    API_SERVER_INDEXES,
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    INDEX_STATUS_METADATA_KEY,
    INDEXES_TO_BE_DELETED,
    IS_CATEGORIZATION_ENABLED_METADATA_KEY,
    ORGANIZATION_DOMAIN_INFO_COLLECTION_NAME,
    IS_CLIENT_API_ENABLED_METADATA_KEY,
    POST_SENTIMENT_INDEXES,
    POST_SUMMARY_INDEXES,
    QUERY_ARCHIVER_RETRIEVER_TOPIC_ID,
    QUERY_COLLECTION_NAME,
    QUERY_COMPLETION_MSG_TEMPLATE,
    QUERY_MIGRATOR_TOPIC_ID,
    QUERY_OPERATION_TYPE_METADATA_KEY,
    QUERY_PROJECTION_ATTRIBUTES,
    CAPABILITIES_MARKER,
    PROJECT_ID,
    APPEND_TOPIC_ID,
    DATA_TRANSFORMER_TOPIC_ID,
    RAC_COLLECTION_NAME_METADATA_KEY,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    RAC_TRANSFORM_COLLECTION_SUFFIX,
    SENTIMENT_TOPIC_ID,
    SENTIMENT_MULTI_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    BIGQUERY_LINK,
    QUERY_OPTIMIZER_TOPIC_ID,
    BIG_QUERY_ENABLED_METADATA_KEY,
    ANALYSIS_STATUS_METADATA_KEY,
    QUERY_RETRIEVER_RAC_STATUS_METADATA_KEY,
    QUERY_RETRIEVER_RAC_TRANSFORM_STATUS_METADATA_KEY,
    YT_COMMENTS_DATA_UNIFIER_TOPIC_ID,
    YT_COMMENTS_HYDRATION_TOPIC_ID,
    FILE_UPLOAD_DATA_UNIFIER_TOPIC_ID,
    FILE_UPLOAD_HYDRATION_TOPIC_ID,
    CLEANSING_TOPIC_ID,
    ENCAPSULATION_TOPIC_ID,
    CONSUMERS_BEFORE_ENCAPSULATION,
    DEFAULT_STRING,
    QUERY_THROTTLER_TOPIC_ID,
    CAPABILITIES_DB_NAME,
    CAPABILITIES_MONGO_URI,
    CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME,
    POD_IP,
    IS_DELETED_META_DATA_KEY,
    QUERY_FILTER_CLASSIFIER_TOPIC_ID,
)
from utils.pubsub_publisher import publish_pubsub_message
from utils.common_utils import (
    check_query_status,
    create_indexes,
    delete_indexes,
    get_organization_info,
    get_query_config,
    toggle_marker_status,
    update_query_metadata,
    trigger_execution_via_http,
)
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_db,
    get_mongodb_collection,
)
from utils.logger import logger
from utils.utilities import (
    DataSourceId,
    DiagnosticActionType,
    DiagnosticStatus,
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    MetaContainer,
    OrganizationAccountInfo,
    PublisherType,
    QueryOperationType,
    CollectionType,
    QueryArchiverRetrieverStatus,
    RacTransform,
    VmConsumers,
    QueryStatus,
)


def is_consumers_completed_for_marker(
    collection: Collection, encapsulation_marker_id: str, status_columns: List[str]
) -> bool:
    """
    Check if all specified consumer status columns are set to 'COMPLETED'
    for a given encapsulation_marker_id.

    Parameters:
    - collection (pymongo.collection.Collection): The encapsulation marker MongoDB collection.
    - encapsulation_marker_id (str): The encapsulation marker ID to search for.
    - status_columns (List[str]): List of column names representing consumer statuses
                                to be checked for 'COMPLETED'.

    Returns:
    - bool: True if all specified status columns are 'COMPLETED' for particular encapsulation marker, otherwise False.

    Exception Handling:
    - Exception: Raised if an error occurs while checking the encapsulation marker statuses for the given consumers.
    """
    try:
        query = {"_id": ObjectId(encapsulation_marker_id)}
        # Add conditions for each status column
        for column in status_columns:
            query[column] = EncapsulationMarkerStatus.COMPLETED.value

        record = collection.find_one(query)
        return record is not None

    except Exception as e:
        message = (
            f"Error checking the {encapsulation_marker_id} encapsulation marker id status "
            f"for consumers {status_columns}: {e}"
        )
        raise Exception(message) from e


def complete_sequence_coordination(
    encapsulation_marker_collection: Collection,
    encapsulation_marker_id: str,
    query_id: str,
    query_name: str,
    query_collection: Collection,
    query_created_at: datetime,
    published_time: datetime,
    meta_container: MetaContainer,
) -> None:
    """
    Mark the sequence coordinator status as completed for a specific encapsulation
    marker ID and query ID, and send a diagnostic message for completion, if the
    status check confirms completion for all encapsulation markers.

    Parameters:
    - encapsulation_marker_collection (pymongo.collection.Collection): The encapsulation
        marker MongoDB collection object containing encapsulation markers data.
    - encapsulation_marker_id (str): ID of the encapsulation marker to update.
    - query_id (str): ID of the query for which to complete the sequence coordination.
    - query_name (str): Name of the query for which to complete the sequence coordination.
    - query_collection (pymongo.collection.Collection): The query MongoDB collection object
        containing the query data.
    - query_created_at (datetime): The creation time of the query when the query was created.
    - published_time (datetime): The completion time of the query.

    Returns:
    - None

    Exception Handling:
    - None
    """
    toggle_marker_status(
        encapsulation_marker_collection,
        encapsulation_marker_id,
        query_id,
        EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value,
        EncapsulationMarkerStatus.COMPLETED.value,
    )

    if check_query_status(
        encapsulation_marker_collection,
        query_id,
        EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value,
        EncapsulationMarkerStatus.COMPLETED.value,
    ):
        message = QUERY_COMPLETION_MSG_TEMPLATE.format(
            query_name=query_name, query_id=query_id
        )
        meta_container.send_diagnostic(
            DiagnosticActionType.INFO.value,
            DiagnosticStatus.INFO.value,
            message,
        )

        message = f"Capabilities sequence coordinator script completed successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.COMPLETED.value,
            message,
        )

        set_query_execution_time(
            query_collection,
            query_created_at,
            published_time,
            query_id,
        )


def grant_dataset_access_with_service_account(
    project_id: str, dataset_id: str, email: str, role: Optional[str] = "READER"
) -> None:
    """
    This function updates the access control list (ACL) of a BigQuery dataset to grant access
    to a user via their email address. The user is assigned a specified role,
    which defaults to "READER" if not provided.

    Parameters:
    - project_id (str): The ID of the Google Cloud project containing the dataset.
    - dataset_id (str): The ID of the BigQuery dataset to which access is being granted.
    - email (str): The email address of the user to whom access is being granted.
    - role (str, optional): The role to assign to the user. Defaults to "READER".
        Other possible roles include "WRITER" and "OWNER".

    Returns:
    - None

    Exception Handling:
    - Logs a warning and skips if any error occurs while updating permission.
    """
    try:
        client = bigquery.Client(project=project_id)

        # Get the dataset reference
        dataset_ref = client.dataset(dataset_id)

        # Get the dataset object
        dataset = client.get_dataset(dataset_ref)

        # Modify the access entries
        access_entries = list(dataset.access_entries)
        access_entries.append(bigquery.AccessEntry(role, "userByEmail", email))
        dataset.access_entries = access_entries

        # Update the dataset with the new access entries
        client.update_dataset(dataset, ["access_entries"])

        print(f"Access granted to {email} with role {role}.")

    except Exception as e:
        logger.warning(
            f"Failed to grant access to {email} for dataset {dataset_id} in project {project_id}: {e}"
        )


def set_query_execution_time(
    query_collection: Collection,
    query_created_at: datetime,
    published_time: datetime,
    query_id_str: str,
) -> None:
    """
    Calculate and set the QUERY EXECUTION TIME for the completed query in the query collection.

    Parameters:
    - query_collection (pymongo.collection.Collection): The query MongoDB collection object
        containing the query data.
    - query_created_at (datetime): The creation time of the query when the query was created.
    - published_time (datetime): The completion time of the query.
    - query_id_str (str): ID of the query for which to complete the sequence coordination.

    Returns:
    - None
    """
    query_created_at = query_created_at.replace(tzinfo=timezone.utc)
    query_execution_time = (published_time - query_created_at).seconds

    update_query_metadata(
        query_collection,
        query_id_str,
        meta_data_key="QUERY_EXECUTION_TIME",
        value=query_execution_time,
    )


def get_unprocessed_index_set(
    query_id: str, query_collection: Collection, index_specs_info: dict
) -> str | None:
    """
    Retrieves the name of the next unprocessed index set for a given query ID.

    - This function fetches the configuration for a specific query from the query collection,
    extracts the metadata related to indexing, and checks the status of each index set
    in query collection metadata. If an index set has not been processed yet, its name is returned.
    If all index sets have been processed, it returns `None`.

    Parameters:
    - query_id (str): Unique identifier for a particular query.
    - query_collection (pymongo.collection.Collection): The MongoDB query collection to
        get index status information.
    - index_specs_info (dict): A dict consists of list of dictionaries where each
        dictionary specifies an index. Each dict should contain 'fields'
        (list of tuples of field names and types)

    Returns:
    - str or None: The name of the next unprocessed index set if found, or
        `None` if all sets are processed.

    Exception Handling:
    - None
    """
    query_config = get_query_config(
        query_collection, query_id, {f"meta_data.{INDEX_STATUS_METADATA_KEY}": 1}
    )
    query_meta_data = query_config["meta_data"]
    query_meta_data = (
        query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
    )

    processed_sets = query_meta_data.get(INDEX_STATUS_METADATA_KEY, {})

    for index_set_name in index_specs_info:
        if index_set_name not in processed_sets or not processed_sets[
            index_set_name
        ].get("is_index_set_processed"):
            logger.info("Next unprocessed index set: %s", index_set_name)
            return index_set_name


def process_indexes(
    query_id: str,
    indexed_collection: Collection,
    query_collection: Collection,
    index_specs_info: dict,
) -> bool:
    """
    Processes the next unprocessed index set for a given query ID by creating indexes
    in the specified indexed collection, updating the query collection's metadata to reflect
    the index set processing status, and determining if any further index sets remain.

    Parameters:
    - query_id (str): ID of the query for which to process indexes.
    - indexed_collection (pymongo.collection.Collection): The MongoDB collection where
        indexes will be created.
    - query_collection (pymongo.collection.Collection): The MongoDB query collection to update.
    - index_specs_info (dict): A dict consists of list of dictionaries where each dictionary
        specifies an index. Each dict should contain 'fields'
        (list of tuples of field names and types)

    Returns:
    - bool: `True` if there are more index sets left to be processed, `False` otherwise.
    """
    index_set_name = get_unprocessed_index_set(
        query_id, query_collection, index_specs_info
    )
    if not index_set_name:
        logger.info(
            "All index sets have been processed for query ID: %s",
            query_id,
        )
        return False

    logger.info("Processing %s...", index_set_name)

    index_specs = index_specs_info[index_set_name]
    create_indexes(indexed_collection, index_specs)
    update_query_metadata(
        query_collection,
        query_id,
        f"{INDEX_STATUS_METADATA_KEY}.{index_set_name}.is_index_set_processed",
        True,
    )

    logger.info("Completed %s.", index_set_name)

    # Check if index sets are remaining for particular query ID
    index_set_name = get_unprocessed_index_set(
        query_id, query_collection, index_specs_info
    )
    if index_set_name:
        logger.info(
            "Loopback required for remaining index sets for query ID: %s",
            query_id,
        )
        return True

    return False


def trigger_query_archiver(
    query_collection: Collection, query_id: str, payload: dict
) -> None:
    """
    Triggers the query archiver by updating the query metadata and notifying the consumer.

    - This function updates the metadata of a query in the given query collection to indicate
    that it is ready for archiving. It then sends a notification to a Pub/Sub topic with the
    provided payload, allowing the query archiver consumer to process the archival.

    Parameters:
    - query_collection (pymongo.collection.Collection): The MongoDB collection
        containing query metadata.
    - query_id (str): Unique identifier of the query to be archived.
    - payload (dict): The message payload to be sent to the query archiver consumer.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Update query metadata to mark it as ready for archiving
    update_query_metadata(
        query_collection,
        query_id,
        QUERY_OPERATION_TYPE_METADATA_KEY,
        QueryOperationType.ARCHIVE.value,
    )

    # Log the operation
    logger.info(
        "Publishing message to Pub/Sub topic '%s' for archiving RAC collection (query_id: %s)",
        QUERY_ARCHIVER_RETRIEVER_TOPIC_ID,
        query_id,
    )

    # Publish the message to Pub/Sub
    publish_pubsub_message(PROJECT_ID, QUERY_ARCHIVER_RETRIEVER_TOPIC_ID, payload)


def create_search_index(collection: Collection) -> str | None:
    """
    Creates a dynamic MongoDB Atlas Search index for the specified collection.
    It doesn't specify explicit field types, allowing dynamic mapping.

    :param collection: The PyMongo Collection object.
    :return: The name of the created search index or None on failure.
    """
    try:
        search_index_definition = {
            "definition": {
                "mappings": {
                    "dynamic": False,
                    "fields": {
                        RacTransform.Tellagence_Date.value: { "type": "date" },
                        RacTransform.YT_author.value: { "type": "string" },
                        RacTransform.Tellagence_Text.value: { "type": "string" },
                        RacTransform.theme_summary.value: { "type": "string" },
                        RacTransform.story_summary.value: { "type": "string" },
                        RacTransform.cluster_summary.value: { "type": "string" }   
                    },
                }
            },
            "name": "search_index",
        }

        index_name = collection.create_search_index(search_index_definition)
        print(f"Search index '{index_name}' created successfully!")
        return index_name

    except Exception as e:
        print(f"Failed to create search index: {e}")
        return None


def create_query_indexes(query_id, collection, query_collection, payload):
    """
    Create indexes for a given query in a MongoDB collection to enhance API server performance
    and update query metadata upon completion.

    Parameters:
    - query_id (str): ID of the query for which indexes are being created.
    - collection (pymongo.collection.Collection): The MongoDB collection to create indexes on.
    - query_collection (pymongo.collection.Collection): The MongoDB collection containing query metadata.
    - payload (dict): Data to publish if loopback is required for remaining index sets.

    Returns:
    - bool: True if loopback is required, otherwise None.

    Exception Handling:
    - None
    """
    # Create indexes for API server performance in batches
    is_loop_back_required = process_indexes(
        query_id,
        collection,
        query_collection,
        API_SERVER_INDEXES,
    )
    if is_loop_back_required:
        publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
        logger.info(
            "Published loopback message for remaining index sets for query Id: %s",
            query_id,
        )
        return True

    create_search_index(collection)
    update_query_metadata(
        query_collection,
        query_id,
        meta_data_key=ANALYSIS_STATUS_METADATA_KEY,
        value=DiagnosticStatus.COMPLETED.value,
    )
    update_query_metadata(
        query_collection,
        query_id,
        meta_data_key=QUERY_STATUS_METADATA_KEY,
        value=QueryStatus.READY.value,
    )

    # Add NEW flag to query metadata
    update_query_metadata(
        query_collection,
        query_id,
        meta_data_key="NEW",
        value=True,
    )


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered by a CloudEvent.

    This function processes data from a CloudEvent payload, performs several operations :

    - Get query_id and organization_id in payload.
    - Fetch organization information from OrganizationAccountInfo using organization_id_str.
    - Fetch mongodb_url , mongo_db_client and organization_db using utils functions.
    - Fetch query_collection and query_config using utils get_mongodb_collection and
        get_query_config functions.
    - From query_config get the meta_data get the CAPABILITIES_MARKER
    - If CAPABILITIES_MARKER is present is meta_data log it is already present
    - If not check for CAPABILITIES_PROCESSING status and CAPABILITIES_PROCESSING status is
        true then added those in CAPABILITIES_MARKER.
    - Trues fields are joined by underscore (_) and converted to lower case in CAPABILITIES_MARKER.
    - Then updated the CAPABILITIES_MARKER in the meta_data of collection.
    - Set the payload with capabilities_marker
    - Perform required operations based on the publisher type:
        - For 'api_server': Publish a message to the query throttler topic ID.
        - For 'query_throttler': If pod IP exists, publish to data encapsulation topic ID otherwise, trigger hydration for the particular data source.
        - For 'hydration': If data source is YT comments or file upload, publish a message to data source specific data unifier topic ID.
        - For 'data_unifier': Publish message to cleansing topic ID.
        - For 'cleansing': Publish message to query_throttler topic ID.
        - For 'data_encapsulation': Initiate the similarity stage by sending an HTTP request to the pod at the specified IP address.
        - For 'similarity': Initiate the discover stage by sending an HTTP request to the pod at the specified IP address.
        - For 'discover': Initiate the summarization stage by sending an HTTP request to the pod at the specified IP address.
        - For 'summarization':
            - Publish message to the sentiment multi topic ID.
            - If categorization is enabled for YT comments/file upload, create categorization indexes and
                trigger categorization tool via HTTP to the pod.
        - For 'sentiment_multi': Publish message to the sentiment topic ID.
        - For 'sentiment' or 'categorization_tool':
            - If categorization is disabled OR both sentiment and categorization tool statuses are COMPLETED for the encapsulation marker,
            create post-sentiment indexes, publish message to query optimizer topic ID and toggle marker status to COMPLETED.
        - For 'query_optimizer': Publish message to query throttler (for pod deletion), and publish message to the data transformer topic ID.
        - For 'data_transformer':
            - Publish a message to the append topic ID if `is_big_query_enabled` is `True`.
            - Publish a message to the query migrator topic ID if `is_client_api_enabled` is `True`.
            - Trigger the query archiver to handle the RAC collection archival and complete the sequence coordination process.
        - For 'append': Mark BigQuery status COMPLETED, optionally grant dataset access to the user email, and persist the BigQuery link.
        - For 'query_migrator': After data migration, create indexes for the query in the client API RAC transform collection.

    Parameters:
    - cloud_event (google.cloud.functions.Context): CloudEvent data provided by
        the function framework.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Raises:
    - Exception: If any error occurs during the execution process.
    """
    mongodb_client = None
    client_api_mongodb_connection = None
    mongodb_client_capabilities = None
    try:
        meta_container = MetaContainer()

        # parse the payload and extract query_id and organization_id from payload
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )

        published_time = datetime.fromisoformat(
            cloud_event.data.get("message").get("publishTime")
        )

        logger.info("Message received successfully: %s", payload)

        meta_container.set_payload_info(payload)

        encapsulation = payload.get("encapsulation", {})
        encapsulation_marker_id = encapsulation.get("_id")

        data_source_id = payload["data_source_id"]
        organization_id_str = payload["organization_id"]
        query_id_str = payload["query_id"]
        publisher_type = payload["publisher_type"]
        collection_type = payload.get("collection_type")

        # Fetch organization account information
        org_account_info = OrganizationAccountInfo(organization_id_str)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        # Establish MongoDB connection
        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)

        # Retrieve query configuration
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )
        query_config = get_query_config(
            query_collection, query_id_str, QUERY_PROJECTION_ATTRIBUTES
        )

        if query_config is None:
            message = f"No configuration found for query_id: {query_id_str}"
            logger.warning(message)
            return {"status": 404, "message": message}

        mongodb_client_capabilities = get_mongodb_client(CAPABILITIES_MONGO_URI)
        capabilities_db = get_mongodb_db(
            mongodb_client_capabilities, CAPABILITIES_DB_NAME
        )

        organization_domain_info_collection = get_mongodb_collection(
            capabilities_db, ORGANIZATION_DOMAIN_INFO_COLLECTION_NAME
        )
        query_throttler_collection = get_mongodb_collection(
            capabilities_db, CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME
        )

        # Get organization information
        organization_domain_info = get_organization_info(
            organization_domain_info_collection, organization_id_str
        )

        is_categorization_enabled = organization_domain_info.get("meta_data", {}).get(
            IS_CATEGORIZATION_ENABLED_METADATA_KEY
        )
        is_big_query_enabled = organization_domain_info.get("meta_data", {}).get(
            BIG_QUERY_ENABLED_METADATA_KEY
        )
        is_client_api_enabled = organization_domain_info.get("meta_data", {}).get(
            IS_CLIENT_API_ENABLED_METADATA_KEY
        )
        query_name = query_config.get("name", "")

        # set the meta data
        meta_container.set_meta_data(query_config["meta_data"])

        rac_collection_name = meta_container.meta_data.get(
            RAC_COLLECTION_NAME_METADATA_KEY
        )
        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)

        # Set default values
        encapsulation_marker_collection = None
        query_operation_type = None
        client_api_query_collection = None
        client_api_rac_transform_collection = None
        rac_transform_collection = None
        capabilities_marker = DEFAULT_STRING
        encapsulation_marker_collection_name = DEFAULT_STRING
        rac_transform_collection_name = DEFAULT_STRING
        pod_ip = meta_container.meta_data.get(POD_IP)

        if publisher_type not in CONSUMERS_BEFORE_ENCAPSULATION:
            capabilities_marker = meta_container.meta_data.get(CAPABILITIES_MARKER, "")

            query_operation_type = meta_container.meta_data.get(
                QUERY_OPERATION_TYPE_METADATA_KEY
            )

            rac_transform_collection_name = meta_container.meta_data.get(
                RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
                f"{rac_collection_name}_{RAC_TRANSFORM_COLLECTION_SUFFIX}",
            )
            rac_transform_collection = get_mongodb_collection(
                organization_db, rac_transform_collection_name
            )
            encapsulation_marker_collection_name = meta_container.meta_data[
                ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
            ]
            encapsulation_marker_collection = get_mongodb_collection(
                organization_db, encapsulation_marker_collection_name
            )

            if is_client_api_enabled:
                # Establishing connection to client API's MongoDB RAC transform collection
                client_api_mongodb_url = org_account_info.client_api_mongodb_url
                client_api_mongodb_connection = get_mongodb_client(
                    client_api_mongodb_url
                )
                client_api_organization_db = get_mongodb_db(
                    client_api_mongodb_connection, organization_db_name
                )
                client_api_query_collection = get_mongodb_collection(
                    client_api_organization_db, QUERY_COLLECTION_NAME
                )
                client_api_rac_transform_collection = get_mongodb_collection(
                    client_api_organization_db, rac_transform_collection_name
                )

            if check_query_status(
                encapsulation_marker_collection,
                query_id_str,
                EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value,
                EncapsulationMarkerStatus.INACTIVE.value,
            ):
                message = f"Capabilities sequence coordinator script started successfully for query_id: {query_id_str}"
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.PENDING.value,
                    message,
                )

                # if capabilities marker is present
                if capabilities_marker:
                    logger.info(
                        "CAPABILITIES_MARKER already updated for document with query_id: %s",
                        query_id_str,
                    )
                else:
                    # check for CAPABILITIES_PROCESSING object in meta_data to create capabilities_marker
                    check_data = meta_container.meta_data.get(
                        "CAPABILITIES_PROCESSING", {}
                    )
                    for key, value in check_data.items():
                        if value:
                            if capabilities_marker:
                                capabilities_marker += "_"
                            capabilities_marker += key.lower()

                    # update capabilities marker in query meta
                    update_query_metadata(
                        query_collection,
                        query_id_str,
                        meta_data_key=CAPABILITIES_MARKER,
                        value=capabilities_marker,
                    )

            toggle_marker_status(
                encapsulation_marker_collection,
                encapsulation_marker_id,
                query_id_str,
                EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value,
                EncapsulationMarkerStatus.PENDING.value,
            )
            # Set the capabilities_marker in the payload
            payload["capabilities_marker"] = capabilities_marker

        # Coordinate the sequence of operations based on the type of publisher
        match publisher_type:
            case PublisherType.API_SERVER.value:
                logger.info(
                    "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                    capabilities_marker,
                    QUERY_THROTTLER_TOPIC_ID,
                )
                publish_pubsub_message(PROJECT_ID, QUERY_THROTTLER_TOPIC_ID, payload)

            case PublisherType.QUERY_THROTTLER.value:
                if pod_ip:
                    # Trigger data encapsulation process
                    logger.info(
                        "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                        capabilities_marker,
                        ENCAPSULATION_TOPIC_ID,
                    )
                    publish_pubsub_message(PROJECT_ID, ENCAPSULATION_TOPIC_ID, payload)
                else:
                    # Trigger hydration process
                    match data_source_id:
                        case DataSourceId.YT_COMMENTS.value:
                            logger.info(
                                "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                                capabilities_marker,
                                YT_COMMENTS_HYDRATION_TOPIC_ID,
                            )
                            message = f"Capabilities youtube comments hydration script started successfully for query_id: {query_id_str}"
                            meta_container.send_diagnostic(
                                DiagnosticActionType.INSERT.value,
                                DiagnosticStatus.INACTIVE.value,
                                message,
                            )
                            publish_pubsub_message(
                                PROJECT_ID, YT_COMMENTS_HYDRATION_TOPIC_ID, payload
                            )

                        case DataSourceId.FILE_UPLOAD.value:
                            logger.info(
                                "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                                capabilities_marker,
                                FILE_UPLOAD_HYDRATION_TOPIC_ID,
                            )
                            message = f"Capabilities File upload hydration script started successfully for query_id: {query_id_str}"
                            meta_container.send_diagnostic(
                                DiagnosticActionType.INSERT.value,
                                DiagnosticStatus.INACTIVE.value,
                                message,
                            )
                            publish_pubsub_message(
                                PROJECT_ID, FILE_UPLOAD_HYDRATION_TOPIC_ID, payload
                            )

            case PublisherType.HYDRATION.value:
                if data_source_id == DataSourceId.YT_COMMENTS.value:
                    logger.info(
                        "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                        capabilities_marker,
                        YT_COMMENTS_DATA_UNIFIER_TOPIC_ID,
                    )
                    publish_pubsub_message(
                        PROJECT_ID, YT_COMMENTS_DATA_UNIFIER_TOPIC_ID, payload
                    )

                if data_source_id == DataSourceId.FILE_UPLOAD.value:
                    logger.info(
                        "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                        capabilities_marker,
                        FILE_UPLOAD_DATA_UNIFIER_TOPIC_ID,
                    )
                    publish_pubsub_message(
                        PROJECT_ID, FILE_UPLOAD_DATA_UNIFIER_TOPIC_ID, payload
                    )

                publish_pubsub_message(
                    PROJECT_ID, QUERY_FILTER_CLASSIFIER_TOPIC_ID, payload
                )

            case PublisherType.DATA_UNIFIER.value:
                logger.info(
                    "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                    capabilities_marker,
                    CLEANSING_TOPIC_ID,
                )
                publish_pubsub_message(PROJECT_ID, CLEANSING_TOPIC_ID, payload)

            case PublisherType.CLEANSING.value:
                logger.info(
                    "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                    capabilities_marker,
                    QUERY_THROTTLER_TOPIC_ID,
                )
                publish_pubsub_message(PROJECT_ID, QUERY_THROTTLER_TOPIC_ID, payload)

            case PublisherType.DATA_ENCAPSULATION.value:  # run similarity
                logger.info("POD_IP: %s", pod_ip)

                next_consumer = VmConsumers.SIMILARITY_VM.value
                logger.info(
                    "Triggering execution for capabilities marker: '%s' via HTTP to pod: %s with consumer: %s",
                    capabilities_marker,
                    pod_ip,
                    next_consumer,
                )
                trigger_execution_via_http(pod_ip, next_consumer, payload)

            case PublisherType.SIMILARITY.value:  # run discover
                next_consumer = VmConsumers.DISCOVER.value
                logger.info(
                    "Triggering execution for capabilities marker: '%s' via HTTP to pod: %s with consumer: %s",
                    capabilities_marker,
                    pod_ip,
                    next_consumer,
                )
                trigger_execution_via_http(pod_ip, next_consumer, payload)

            case PublisherType.DISCOVER.value:  # run summarization_vm
                next_consumer = VmConsumers.SUMMARIZATION_VM.value
                logger.info(
                    "Triggering execution for capabilities marker: '%s' via HTTP to pod: %s with consumer: %s",
                    capabilities_marker,
                    pod_ip,
                    next_consumer,
                )
                trigger_execution_via_http(pod_ip, next_consumer, payload)

            case PublisherType.SUMMARIZATION.value:
                create_indexes(rac_collection, POST_SUMMARY_INDEXES)
                logger.info(
                    "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                    capabilities_marker,
                    SENTIMENT_MULTI_TOPIC_ID,
                )

                # Trigger the sentiment multi
                publish_pubsub_message(PROJECT_ID, SENTIMENT_MULTI_TOPIC_ID, payload)

                # If categorization is enabled for the data source, trigger categorization tool
                if (
                    data_source_id
                    in [DataSourceId.YT_COMMENTS.value, DataSourceId.FILE_UPLOAD.value]
                    and is_categorization_enabled
                ):
                    create_indexes(rac_collection, CATEGORIZATION_INDEXES)
                    next_consumer = VmConsumers.CATEGORIZATION_TOOL.value
                    logger.info(
                        "Triggering execution for capabilities marker: '%s' via HTTP to pod: %s with consumer: %s",
                        capabilities_marker,
                        pod_ip,
                        next_consumer,
                    )
                    trigger_execution_via_http(pod_ip, next_consumer, payload)

            case PublisherType.SENTIMENT_MULTI.value:
                logger.info(
                    "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                    capabilities_marker,
                    SENTIMENT_TOPIC_ID,
                )

                publish_pubsub_message(PROJECT_ID, SENTIMENT_TOPIC_ID, payload)

            case (
                PublisherType.SENTIMENT.value | PublisherType.CATEGORIZATION_TOOL.value
            ):
                # Publish if categorization is disabled,
                # OR if categorization is enabled AND both sentiment & categorization tool statuses are "COMPLETED"
                # for the encapsulation marker
                if (not is_categorization_enabled) or is_consumers_completed_for_marker(
                    encapsulation_marker_collection,
                    encapsulation_marker_id,
                    [
                        EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value,
                        EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value,
                    ],
                ):
                    create_indexes(rac_collection, POST_SENTIMENT_INDEXES)
                    logger.info(
                        "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                        capabilities_marker,
                        QUERY_OPTIMIZER_TOPIC_ID,
                    )

                    publish_pubsub_message(
                        PROJECT_ID, QUERY_OPTIMIZER_TOPIC_ID, payload
                    )
                    toggle_marker_status(
                        encapsulation_marker_collection,
                        encapsulation_marker_id,
                        query_id_str,
                        EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value,
                        EncapsulationMarkerStatus.COMPLETED.value,
                    )

            case PublisherType.QUERY_OPTIMIZER.value:
                # Invoke query throttler to delete the POD
                query_throttler_collection.update_one(
                    {"query_id": ObjectId(query_id_str)},
                    {"$set": {"query_status": QueryStatus.COMPLETED.value}},
                )
                publish_pubsub_message(PROJECT_ID, QUERY_THROTTLER_TOPIC_ID, payload)

                # Delete indexes from RAC collection
                delete_indexes(rac_collection, INDEXES_TO_BE_DELETED)
                logger.info(
                    "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                    capabilities_marker,
                    DATA_TRANSFORMER_TOPIC_ID,
                )

                publish_pubsub_message(PROJECT_ID, DATA_TRANSFORMER_TOPIC_ID, payload)

            case PublisherType.DATA_TRANSFORMER.value:
                is_loopback_required = create_query_indexes(
                    query_id_str, rac_transform_collection, query_collection, payload
                )
                if is_loopback_required:
                    return {"status": 200}

                if is_client_api_enabled:
                    logger.info(
                        "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s as client API is enabled",
                        capabilities_marker,
                        QUERY_MIGRATOR_TOPIC_ID,
                    )
                    publish_pubsub_message(PROJECT_ID, QUERY_MIGRATOR_TOPIC_ID, payload)

                if is_big_query_enabled:
                    logger.info(
                        "Publishing message for capabilities marker: '%s' to Pub/Sub topic: %s",
                        capabilities_marker,
                        APPEND_TOPIC_ID,
                    )
                    publish_pubsub_message(PROJECT_ID, APPEND_TOPIC_ID, payload)

                # Trigger the query archiver to handle the RAC collection archival
                trigger_query_archiver(query_collection, query_id_str, payload)

                complete_sequence_coordination(
                    encapsulation_marker_collection,
                    encapsulation_marker_id,
                    query_id_str,
                    query_name,
                    query_collection,
                    query_config["created_at"],
                    published_time,
                    meta_container,
                )

            case PublisherType.APPEND.value:
                update_query_metadata(
                    query_collection,
                    query_id_str,
                    meta_data_key="BIGQUERY_STATUS",
                    value=DiagnosticStatus.COMPLETED.value,
                )

                bigquery_link = BIGQUERY_LINK.format(
                    organization_db_name, rac_transform_collection_name
                )
                email = meta_container.meta_data.get("USER_EMAIL", "")

                if email:
                    grant_dataset_access_with_service_account(
                        PROJECT_ID, organization_db_name, email
                    )

                update_query_metadata(
                    query_collection,
                    query_id_str,
                    meta_data_key="BIGQUERY_LINK",
                    value=bigquery_link,
                )

            case PublisherType.QUERY_MIGRATOR.value:
                # Create indexes in client api RAC transform collection after data migration
                is_loopback_required = create_query_indexes(
                    query_id_str,
                    client_api_rac_transform_collection,
                    client_api_query_collection,
                    payload,
                )
                if is_loopback_required:
                    return {"status": 200}

            case PublisherType.QUERY_ARCHIVER_RETRIEVER.value:
                if query_operation_type == QueryOperationType.RETRIEVE.value:
                    is_loopback_required = create_query_indexes(
                        query_id_str,
                        rac_transform_collection,
                        query_collection,
                        payload,
                    )
                    if is_loopback_required:
                        return {"status": 200}

                    query_retriever_status_key = QUERY_RETRIEVER_RAC_STATUS_METADATA_KEY
                    if collection_type == CollectionType.RAC_TRANSFORM.value:
                        query_retriever_status_key = (
                            QUERY_RETRIEVER_RAC_TRANSFORM_STATUS_METADATA_KEY
                        )

                    update_query_metadata(
                        query_collection,
                        query_id_str,
                        meta_data_key=query_retriever_status_key,
                        value=QueryArchiverRetrieverStatus.COMPLETED.value,
                    )
                    if collection_type == CollectionType.RAC_TRANSFORM.value:
                        update_query_metadata(
                            query_collection,
                            query_id_str,
                            meta_data_key=IS_DELETED_META_DATA_KEY,
                            value=False,
                        )
                        update_query_metadata(
                            query_collection,
                            query_id_str,
                            meta_data_key=QUERY_STATUS_METADATA_KEY,
                            value=QueryStatus.READY.value,
                        )

            case _:
                logger.warning("Unknown publisher type!")

        return {"status": 200}

    except Exception as e:
        message = f"An error occurred during capabilities sequence coordinator: {e}"
        logger.exception(message)
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        update_query_metadata(
            query_collection,
            query_id_str,
            meta_data_key=QUERY_STATUS_METADATA_KEY,
            value=QueryStatus.FAILED.value,
        )

        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id_str,
            EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value,
            EncapsulationMarkerStatus.FAILED.value,
        )
        return {"status": 400, "message": message}

    finally:
        if mongodb_client is not None:
            mongodb_client.close()

        if mongodb_client_capabilities is not None:
            mongodb_client_capabilities.close()

        if client_api_mongodb_connection is not None:
            client_api_mongodb_connection.close()
