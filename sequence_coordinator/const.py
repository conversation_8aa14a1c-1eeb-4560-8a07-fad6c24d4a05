"""
This module contains constants used in files of sequence coordinator consumer.
"""

import os
from pymongo import ASCENDING, DESCENDING
from utils.utilities import (
    CategorizationProcessingField,
    SentimentProcessingField,
    SummaryContextIdField,
    PublisherType,
    RacTransform
)

DEFAULT_STRING = ""
POD_IP = os.getenv("POD_IP")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
PROJECT_ID = os.environ.get("PROJECT_ID")
APPEND_TOPIC_ID = os.getenv("APPEND_TOPIC_ID")
DATA_TRANSFORMER_TOPIC_ID = os.getenv("DATA_TRANSFORMER_TOPIC_ID")
QUERY_ARCHIVER_RETRIEVER_TOPIC_ID = os.getenv("QUERY_ARCHIVER_RETRIEVER_TOPIC_ID")
QUERY_MIGRATOR_TOPIC_ID = os.getenv("QUERY_MIGRATOR_TOPIC_ID")
SENTIMENT_TOPIC_ID = os.getenv("SENTIMENT_TOPIC_ID")
SENTIMENT_MULTI_TOPIC_ID = os.getenv("SENTIMENT_MULTI_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
CAPABILITIES_MARKER = os.getenv("CAPABILITIES_MARKER")
CAPABILITIES_MONGO_URI = os.getenv("CAPABILITIES_MONGO_URI")
CAPABILITIES_DB_NAME = os.getenv("CAPABILITIES_DB_NAME")

YT_COMMENTS_HYDRATION_TOPIC_ID = os.getenv("YT_COMMENTS_HYDRATION_TOPIC_ID")
YT_COMMENTS_DATA_UNIFIER_TOPIC_ID = os.getenv("YT_COMMENTS_DATA_UNIFIER_TOPIC_ID")
FILE_UPLOAD_HYDRATION_TOPIC_ID = os.getenv("FILE_UPLOAD_HYDRATION_TOPIC_ID")
FILE_UPLOAD_DATA_UNIFIER_TOPIC_ID = os.getenv("FILE_UPLOAD_DATA_UNIFIER_TOPIC_ID")
ENCAPSULATION_TOPIC_ID = os.getenv("ENCAPSULATION_TOPIC_ID")
CLEANSING_TOPIC_ID = os.getenv("CLEANSING_TOPIC_ID")
QUERY_THROTTLER_TOPIC_ID = os.getenv("QUERY_THROTTLER_TOPIC_ID")
QUERY_FILTER_CLASSIFIER_TOPIC_ID = os.getenv("QUERY_FILTER_CLASSIFIER_TOPIC_ID")
CAPABILITIES_MONGO_URI = os.getenv("CAPABILITIES_MONGO_URI")
CAPABILITIES_DB_NAME = os.getenv("CAPABILITIES_DB_NAME")
CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME = os.getenv(
    "CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME"
)

CAPABILITIES_USERS_COLLECTION_NAME = os.getenv("CAPABILITIES_USERS_COLLECTION_NAME")
ORGANIZATION_DOMAIN_INFO_COLLECTION_NAME = os.getenv(
    "ORGANIZATION_DOMAIN_INFO_COLLECTION_NAME"
)
QUERY_OPTIMIZER_TOPIC_ID = os.getenv("QUERY_OPTIMIZER_TOPIC_ID")
RAC_TRANSFORM_COLLECTION_SUFFIX = os.getenv("RAC_TRANSFORM_COLLECTION_SUFFIX")
IS_CLIENT_API_ENABLED_METADATA_KEY = "IS_CLIENT_API_ENABLED"
BIG_QUERY_ENABLED_METADATA_KEY = "IS_BIG_QUERY_ENABLED"
QUERY_MIGRATOR_TOPIC_ID = os.getenv("QUERY_MIGRATOR_TOPIC_ID")
IS_CATEGORIZATION_ENABLED_METADATA_KEY = "IS_CATEGORIZATION_ENABLED"
ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)
ENCAPSULATION_MARKER_FIELD_NAME = "encapsulation_marker"
INDEX_STATUS_METADATA_KEY = "INDEX_STATUS"
QUERY_OPERATION_TYPE_METADATA_KEY = "QUERY_OPERATION_TYPE"
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
ANALYSIS_STATUS_METADATA_KEY = "ANALYSIS_STATUS"
QUERY_STATUS_METADATA_KEY = "STATUS"
QUERY_PROJECTION_ATTRIBUTES = {
    "name": 1,
    "meta_data": 1,
    "source.data_source_id": 1,
    "user_id": 1,
    "source.meta_data": 1,
    "created_at": 1,
}

BIGQUERY_LINK = os.getenv("BIGQUERY_LINK")
QUERY_COMPLETION_MSG_TEMPLATE = (
    "We are pleased to inform you that your query titled '<b>{query_name}</b>' (ID: <b>{query_id}</b>) "
    "has been successfully completed. The data is now available for access in <b>BigQuery</b>."
)

SUMMARIZATION_VM_TOPIC_ID = os.getenv("SUMMARIZATION_VM_TOPIC_ID")
SUMMARIZATION_VM_THERSHOLD = os.getenv("SUMMARIZATION_VM_THERSHOLD")
QUERY_RETRIEVER_RAC_STATUS_METADATA_KEY = "QUERY_RETRIEVER_RAC_STATUS"
QUERY_RETRIEVER_RAC_TRANSFORM_STATUS_METADATA_KEY = (
    "QUERY_RETRIEVER_RAC_TRANSFORM_STATUS"
)
IS_DELETED_META_DATA_KEY = "IS_DELETED"


# Indexes needs to be created for sentiment
POST_SUMMARY_INDEXES = [
    {
        "fields": [
            (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
            (SentimentProcessingField.CLUSTER.value, ASCENDING),
            (SummaryContextIdField.CLUSTER.value, DESCENDING),
        ]
    },
    # TODO: Story and theme sentiment related indexing are currently disabled, as only cluster
    # sentiment indexes are needed. Re-enable if additional sentiment indexing becomes
    # necessary in the future.
    # {
    #     "fields": [
    #         (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
    #         (SentimentProcessingField.STORY.value, ASCENDING),
    #         (SummaryContextIdField.STORY.value, DESCENDING),
    #     ]
    # },
    # {
    #     "fields": [
    #         (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
    #         (SentimentProcessingField.THEME.value, ASCENDING),
    #         (SummaryContextIdField.THEME.value, DESCENDING),
    #     ]
    # },
]

# Indexes needs to be created for query optimizer
POST_SENTIMENT_INDEXES = [{"fields": [(RacTransform.YT_record_type.value, ASCENDING)]}]

# Indexes needs to be created for API server performance
# POST_QUERY_OPTIMIZER_INDEXES = [
#     {
#         "fields": [
#             (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
#             (SummaryContextIdField.THEME.value, ASCENDING),
#             ("theme_sentiment", ASCENDING),
#             ("record_type", ASCENDING),
#         ]
#     },
#     {
#         "fields": [
#             (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
#             (SummaryContextIdField.THEME.value, ASCENDING),
#             ("Stories", ASCENDING),
#             ("story_sentiment", ASCENDING),
#             ("record_type", ASCENDING),
#         ]
#     },
#     {
#         "fields": [
#             (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
#             (SummaryContextIdField.THEME.value, ASCENDING),
#             ("Keyword", ASCENDING),
#             ("record_type", ASCENDING),
#         ]
#     },
#     {
#         "fields": [
#             (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
#             (SummaryContextIdField.THEME.value, ASCENDING),
#             ("Stories", ASCENDING),
#             ("Phrase", ASCENDING),
#             ("record_type", ASCENDING),
#         ]
#     },
# ]

# Indexes needs to be created for API server performance
POST_DATA_TRANSFORMER_INDEXES = [
    {"fields": [(RacTransform.YT_like_count.value, DESCENDING)]},
    {"fields": [(RacTransform.YT_reply_count.value, DESCENDING)]},
]

# Indexes needs to be created for API server performance
POST_APPEND_INDEXES = [
    {"fields": [("quotes_count", DESCENDING)]},
    {"fields": [("share_count", DESCENDING)]},
]

# Indexes needs to be created for categorization tool
CATEGORIZATION_INDEXES = [
    {
        "fields": [
            (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
            (CategorizationProcessingField.THEME.value, ASCENDING),
            (SummaryContextIdField.THEME.value, DESCENDING),
            (RacTransform.YT_video_id.value, ASCENDING),
        ]
    },
]

# Indexes which needs to be deleted post processing
INDEXES_TO_BE_DELETED = [
    "encapsulation_marker_Unique_Cluster_ID_index",
    "encapsulation_marker_Unique_Story_ID_index",
    "encapsulation_marker_themes_stories_cluster_id_index",
    "encapsulation_marker_1_is_cluster_sentiment_processed_1_Unique_Cluster_ID_-1",
    "encapsulation_marker_1_is_story_sentiment_processed_1_Unique_Story_ID_-1",
    "encapsulation_marker_1_is_theme_sentiment_processed_1_Themes_-1",
    "record_type_1",
]

# Indexes needs to be created for API server performance
API_SERVER_INDEXES = {
    "API_SERVER_BATCH_1": [
        {
            "fields": [
                (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
                (SummaryContextIdField.THEME.value, ASCENDING),
                (RacTransform.cluster_sentiment.value, ASCENDING),
                (RacTransform.YT_record_type.value, ASCENDING),
            ]
        },
        {
            "fields": [
                (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
                (SummaryContextIdField.THEME.value, ASCENDING),
                (RacTransform.Stories.value, ASCENDING),
                (RacTransform.cluster_sentiment.value, ASCENDING),
                (RacTransform.YT_record_type.value, ASCENDING),
            ]
        },
        {
            "fields": [
                (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
                (SummaryContextIdField.THEME.value, ASCENDING),
                (RacTransform.Keyword.value, ASCENDING),
                (RacTransform.YT_record_type.value, ASCENDING),
            ]
        },
        {
            "fields": [
                (ENCAPSULATION_MARKER_FIELD_NAME, ASCENDING),
                (SummaryContextIdField.THEME.value, ASCENDING),
                (RacTransform.Stories.value, ASCENDING),
                (RacTransform.Phrase.value, ASCENDING),
                (RacTransform.YT_record_type.value, ASCENDING),
            ]
        },
    ],
    "API_SERVER_BATCH_2": [
        {"fields": [(RacTransform.YT_like_count.value, DESCENDING)]},
        {"fields": [(RacTransform.YT_reply_count.value, DESCENDING)]},
        {"fields": [(RacTransform.video_info_snippet_title.value, ASCENDING)]},
    ],
    "API_SERVER_BATCH_3": [
        {"fields": [("quotes_count", DESCENDING)]},
        {"fields": [("share_count", DESCENDING)]},
        {"fields": [(RacTransform.cluster_sentiment.value, ASCENDING)]},
        {"fields": [(RacTransform.YT_video_id.value, ASCENDING)]},
    ],
    "API_SERVER_BATCH_4": [
        {
            "fields": [
                (RacTransform.Tellagence_Date.value, ASCENDING),
                (RacTransform.cluster_sentiment.value, ASCENDING),
                (RacTransform.YT_like_count.value, ASCENDING),
            ]
        },
        {
            "fields": [
                (RacTransform.Tellagence_Date.value, ASCENDING),
                (RacTransform.YT_video_id.value, ASCENDING),
                (RacTransform.cluster_sentiment.value, ASCENDING),
                (RacTransform.video_info_snippet_title.value, ASCENDING),
                (RacTransform.video_info_snippet_channelTitle.value, ASCENDING),
            ]
        },
        {
            "fields": [
                (RacTransform.Tellagence_Date.value, ASCENDING),
                (RacTransform.YT_video_id.value, ASCENDING),
                (RacTransform.video_info_statistics_viewCount.value, ASCENDING),
                (RacTransform.video_info_statistics_likeCount.value, ASCENDING),
                (RacTransform.YT_like_count.value, ASCENDING),
                (RacTransform.YT_reply_count.value, ASCENDING),
            ]
        },
    ],
    "API_SERVER_BATCH_5": [
        {"fields": [(RacTransform.video_info_snippet_channelTitle.value, ASCENDING)]},
        {"fields": [(RacTransform.YT_record_type.value, ASCENDING)]},
        {"fields": [(RacTransform.Tellagence_ID.value, ASCENDING)]},
    ],
}

CONSUMERS_BEFORE_ENCAPSULATION = [
    PublisherType.API_SERVER.value,
    PublisherType.HYDRATION.value,
    PublisherType.DATA_UNIFIER.value,
    PublisherType.CLEANSING.value,
]
