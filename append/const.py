"""
This module contains constants used in the main.py file of append consumer.
"""

import os
from utils.utilities import (
    CategorizationProcessingField,
    SentimentProcessingField,
    SummaryProcessingField,
    RacTransform,
)

APPEND_TOPIC_ID = os.getenv("APPEND_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
PROJECT_ID = os.getenv("PROJECT_ID")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")

DATE_UTC_COLUMN = RacTransform.date_utc.value
APPEND_OFFSET_METADATA_KEY = "APPEND_OFFSET"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
BIG_QUERY_SCHEMA_METADATA_KEY = "BIG_QUERY_SCHEMA"
RAC_TRANSFORM_VOLUME_METADATA_KEY = "RAC_TRANSFORM_VOLUME"
RAC_TRANSFORM_QUERY_PROJECTION = {
    "embedding": 0,
    "Categorization_Embedding": 0,
    "is_embedded": 0,
    "POS": 0,
    SentimentProcessingField.CLUSTER.value: 0,
    SentimentProcessingField.STORY.value: 0,
    SentimentProcessingField.THEME.value: 0,
    SummaryProcessingField.CLUSTER.value: 0,
    SummaryProcessingField.STORY.value: 0,
    SummaryProcessingField.THEME.value: 0,
    CategorizationProcessingField.THEME.value: 0,
}
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{APPEND_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_VOLUME_METADATA_KEY}": 1,
    f"source.meta_data": 1,
}
TYPE_MAPPING = {
    RacTransform.snippet_canRate.value: "boolean",
    RacTransform.snippet_canReply.value: "boolean",
    RacTransform.snippet_isPublic.value: "boolean",
    RacTransform.snippet_topLevelComment_snippet_canRate.value: "boolean",
    RacTransform.video_info_contentDetails_licensedContent.value: "boolean",
    RacTransform.Tellagence_Date.value: "datetime64[ns]",
    RacTransform.Hashtag_Position.value: "float64",
    RacTransform.cluster.value: "int64",
    RacTransform.EMOJIS_Unique_Count.value: "int64",
    RacTransform.Hashtag_Unique_Count.value: "int64",
    RacTransform.snippet_topLevelComment_snippet_likeCount.value: "int64",
    RacTransform.snippet_totalReplyCount.value: "int64",
    RacTransform.Stories.value: "int64",
    RacTransform.Themes.value: "int64",
    RacTransform.Unique_Cluster_ID.value: "int64",
    RacTransform.Unique_Story_ID.value: "int64",
    RacTransform.video_info_statistics_commentCount.value: "int64",
    RacTransform.video_info_statistics_favoriteCount.value: "int64",
    RacTransform.video_info_statistics_likeCount.value: "int64",
    RacTransform.video_info_statistics_viewCount.value: "int64",
    RacTransform.theme_categorization_cluster_prediction.value: "int64",
}
YOUTUBE_BIG_QUERY_SCHEMA = {
    RacTransform._id.value: "STRING",
    RacTransform.Tellagence_ID.value: "STRING",
    RacTransform.Tellagence_Date.value: ["TIMESTAMP", "DATETIME"],
    RacTransform.Tellagence_Text.value: "STRING",
    RacTransform.YT_etag.value: "STRING",
    RacTransform.YT_kind.value: "STRING",
    RacTransform.YT_record_type.value: "STRING",
    RacTransform.YT_replies.value: "STRING",
    RacTransform.YT_author.value: "STRING",
    RacTransform.YT_author_channel_url.value: "STRING",
    RacTransform.YT_author_profile_image_url.value: "STRING",
    RacTransform.YT_comment_id.value: "STRING",
    RacTransform.YT_like_count.value: "STRING",
    RacTransform.YT_reply_count.value: "STRING",
    RacTransform.YT_video_id.value: "STRING",
    RacTransform.BODY1.value: "STRING",
    RacTransform.EMOJIS.value: "STRING",
    RacTransform.EMOJIS_Unique.value: "STRING",
    RacTransform.EMOJIS_Unique_Count.value: "INTEGER",
    RacTransform.Hashtag.value: "STRING",
    RacTransform.Hashtag_Position.value: "FLOAT",
    RacTransform.Hashtag_Unique.value: "STRING",
    RacTransform.Hashtag_Unique_Count.value: "INTEGER",
    RacTransform.Keyword.value: "STRING",
    RacTransform.Lemitized.value: "STRING",
    RacTransform.Phrase.value: "STRING",
    RacTransform.encapsulation_marker.value: "STRING",
    RacTransform.cluster.value: "INTEGER",
    RacTransform.Stories.value: "INTEGER",
    RacTransform.Themes.value: "INTEGER",
    RacTransform.Unique_Cluster_ID.value: "INTEGER",
    RacTransform.Unique_Story_ID.value: "INTEGER",
    RacTransform.cluster_summary.value: "STRING",
    RacTransform.story_summary.value: "STRING",
    RacTransform.theme_summary.value: "STRING",
    RacTransform.cluster_sentiment.value: "STRING",
    RacTransform.cluster_sentiment_reasoning.value: "STRING",
    RacTransform.snippet_channelId.value: "STRING",
    RacTransform.snippet_videoId.value: "STRING",
    RacTransform.snippet_canReply.value: "BOOLEAN",
    RacTransform.snippet_totalReplyCount.value: "INTEGER",
    RacTransform.snippet_isPublic.value: "BOOLEAN",
    RacTransform.snippet_topLevelComment_kind.value: "STRING",
    RacTransform.snippet_topLevelComment_etag.value: "STRING",
    RacTransform.snippet_topLevelComment_id.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_channelId.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_videoId.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_textDisplay.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_textOriginal.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorDisplayName.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorProfileImageUrl.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorChannelUrl.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_authorChannelId_value.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_canRate.value: "BOOLEAN",
    RacTransform.snippet_topLevelComment_snippet_viewerRating.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_likeCount.value: "INTEGER",
    RacTransform.snippet_topLevelComment_snippet_publishedAt.value: "STRING",
    RacTransform.snippet_topLevelComment_snippet_updatedAt.value: "STRING",
    RacTransform.snippet_textDisplay.value: "STRING",
    RacTransform.snippet_textOriginal.value: "STRING",
    RacTransform.snippet_parentId.value: "STRING",
    RacTransform.snippet_authorDisplayName.value: "STRING",
    RacTransform.snippet_authorProfileImageUrl.value: "STRING",
    RacTransform.snippet_authorChannelUrl.value: "STRING",
    RacTransform.snippet_canRate.value: "BOOLEAN",
    RacTransform.snippet_viewerRating.value: "STRING",
    RacTransform.snippet_likeCount.value: "STRING",
    RacTransform.snippet_publishedAt.value: "STRING",
    RacTransform.snippet_updatedAt.value: "STRING",
    RacTransform.snippet_authorChannelId_value.value: "STRING",
    RacTransform.video_info_snippet_publishedAt.value: "STRING",
    RacTransform.video_info_snippet_channelId.value: "STRING",
    RacTransform.video_info_snippet_title.value: "STRING",
    RacTransform.video_info_snippet_description.value: "STRING",
    RacTransform.video_info_snippet_thumbnails_default_url.value: "STRING",
    RacTransform.video_info_snippet_thumbnails_default_width.value: "STRING",
    RacTransform.video_info_snippet_thumbnails_default_height.value: "STRING",
    RacTransform.video_info_snippet_channelTitle.value: "STRING",
    RacTransform.video_info_snippet_tags.value: "STRING",
    RacTransform.video_info_snippet_categoryId.value: "STRING",
    RacTransform.video_info_snippet_liveBroadcastContent.value: "STRING",
    RacTransform.video_info_snippet_defaultLanguage.value: "STRING",
    RacTransform.video_info_snippet_defaultAudioLanguage.value: "STRING",
    RacTransform.video_info_contentDetails_duration.value: "STRING",
    RacTransform.video_info_contentDetails_dimension.value: "STRING",
    RacTransform.video_info_contentDetails_definition.value: "STRING",
    RacTransform.video_info_contentDetails_caption.value: "STRING",
    RacTransform.video_info_statistics_viewCount.value: "INTEGER",
    RacTransform.video_info_statistics_likeCount.value: "INTEGER",
    RacTransform.video_info_statistics_favoriteCount.value: "INTEGER",
    RacTransform.video_info_statistics_commentCount.value: "INTEGER",
    f"YT_{RacTransform.date_utc_str.value}": "STRING",
    f"YT_{RacTransform.date_utc.value}": "DATE",
    RacTransform.video_info_contentDetails_contentRating.value: "STRING",
    RacTransform.video_info_contentDetails_regionRestriction_blocked.value: "STRING",
    RacTransform.video_info_contentDetails_licensedContent.value: "BOOLEAN",
}
FILE_UPLOAD_BIG_QUERY_SCHEMA = {
    RacTransform._id.value: "STRING",
    RacTransform.Tellagence_ID.value: "STRING",
    RacTransform.Tellagence_Date.value: ["TIMESTAMP", "DATETIME"],
    RacTransform.Tellagence_Text.value: "STRING",
    RacTransform.BODY1.value: "STRING",
    RacTransform.EMOJIS.value: "STRING",
    RacTransform.EMOJIS_Unique.value: "STRING",
    RacTransform.EMOJIS_Unique_Count.value: "INTEGER",
    RacTransform.Hashtag.value: "STRING",
    RacTransform.Hashtag_Position.value: "FLOAT",
    RacTransform.Hashtag_Unique.value: "STRING",
    RacTransform.Hashtag_Unique_Count.value: "INTEGER",
    RacTransform.Keyword.value: "STRING",
    RacTransform.Lemitized.value: "STRING",
    RacTransform.Phrase.value: "STRING",
    RacTransform.encapsulation_marker.value: "STRING",
    RacTransform.cluster.value: "INTEGER",
    RacTransform.Stories.value: "INTEGER",
    RacTransform.Themes.value: "INTEGER",
    RacTransform.Unique_Cluster_ID.value: "INTEGER",
    RacTransform.Unique_Story_ID.value: "INTEGER",
    RacTransform.cluster_summary.value: "STRING",
    RacTransform.story_summary.value: "STRING",
    RacTransform.theme_summary.value: "STRING",
    RacTransform.cluster_sentiment.value: "STRING",
    RacTransform.cluster_sentiment_reasoning.value: "STRING",
    f"FL_{RacTransform.date_utc_str.value}": "STRING",
    f"FL_{RacTransform.date_utc.value}": "DATE",
}
