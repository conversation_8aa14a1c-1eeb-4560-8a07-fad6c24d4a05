"""
Module that contains function to validate the schema of a BigQuery table against a provided schema.
"""

from utils.logger import logger
from google.cloud import bigquery
from google.cloud.exceptions import NotFound


def validate_schema_from_table_id(table_id, bigquery_schema):
    """
    Validates the schema of a BigQuery table against a provided schema.
    This function retrieves the schema of a specified BigQuery table and compares it with the provided 'bigquery_schema'. It raises exceptions if validation fails due to missing fields or mismatched field types.

    Parameters:
    - table_id (str): The fully qualified table ID in the format `project.dataset.table`.
    - bigquery_schema (dict): The expected schema to validate against. Keys are field names,
      and values are expected field types. Field types can be a single type (str) or a
      list of acceptable types (list[str]). If `None`, validation proceeds with an empty schema.

    Returns:
    - boolean: The function returns `True` if the schema validation is successful. It raises exceptions for validation failures or errors encountered during execution.


    Exception Handling:
    - Exception: If the table schema does not match the provided schema.
    - Exception: If the table is not found in BigQuery.
    - Exception: For any other errors encountered during validation.
    """
    try:
        client = bigquery.Client()
        if bigquery_schema is None:
            logger.warning(
                "No bigquery_schema provided. Proceeding with an empty schema."
            )
            bigquery_schema = {}

        table = client.get_table(table_id)
        bq_table_columns = {field.name: field.field_type for field in table.schema}

        def matches_common_fields(common_fields):
            """
            Validates that the specified common fields exist in the BigQuery table schema
            and ensures their data types match the expected types. It verifies that each field name exists in the BigQuery table columns and whether the field type matches the expected type(s) accordingly.

            Parameters:
            - common_fields (dict): A dictionary where keys are field names and values are expected field types.
            Field types can be either a single string or a list of acceptable types.

            Returns:
            - bool: Returns `True` if all common fields pass the validation.

            Raises:
            - Exception: If a field is not found in the BigQuery table columns.
            - Exception: If a field's data type does not match the expected type(s).
            """
            for field_name, field_type in common_fields.items():
                # Check if the field name exists in the BigQuery table columns
                if field_name not in bq_table_columns:

                    raise Exception(
                        f"Validation failed: Field '{field_name}' not found in BigQuery table columns."
                    )
                if isinstance(
                    field_type, list
                ):  # Field type is a list of acceptable types
                    if bq_table_columns.get(field_name) not in field_type:

                        raise Exception(
                            f"Validation failed: Field '{field_name}' found but its type '{bq_table_columns.get(field_name)}' "
                            f"does not match any of the valid types: {field_type}."
                        )
                else:  # Non-list field type check
                    if (
                        field_name not in bq_table_columns
                    ):  # Check for field name presence
                        raise Exception(
                            f"Validation failed: Field '{field_name}' not found in BigQuery table columns."
                        )
                    elif (
                        bq_table_columns.get(field_name) != field_type
                    ):  # Field exists but type mismatch
                        raise Exception(
                            f"Validation failed: Field '{field_name}' found but its type '{bq_table_columns.get(field_name)}' "
                            f"does not match the expected type '{field_type}'."
                        )
            return True

        result = matches_common_fields(bigquery_schema)
        if result:
            logger.info(
                f"Schema validation successful: Table '{table_id}' matches the defined big query table schema."
            )
        else:
            raise Exception(f"Table '{table_id}' does not match any schema.")
    except NotFound:
        raise Exception(f"ERROR: Table '{table_id}' not found.")
    except Exception as e:
        raise Exception(
            f"ERROR: An error occurred while validating table '{table_id}': {e}"
        )