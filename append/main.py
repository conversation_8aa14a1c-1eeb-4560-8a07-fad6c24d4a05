"""
This module contains a Cloud Function triggered by a message from a Cloud Pub/Sub topic.
The function processes data from MongoDB, performs data transformation, and inserts 
the results into a BigQuery table(append). It handles a variety of operations such as 
deduplication, data fetching, and loopback processing based on the payload provided 
by the incoming Pub/Sub message.
"""

import base64
import json
from cloudevents.http import Cloud<PERSON>vent
import functions_framework
import pandas as pd
from pandas_gbq import to_gbq, read_gbq
from pandas_gbq.exceptions import GenericGBQException
from const import (
    APPEND_OFFSET_METADATA_KEY,
    APPEND_TOPIC_ID,
    RAC_TRANSFORM_VOLUME_METADATA_KEY,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    BASE_LOOPBACK_THRESHOLD,
    CHUNK_SIZE,
    DATE_UTC_COLUMN,
    PROJECT_ID,
    QUERY_COLLECTION_NAME,
    QUERY_PROJECTION_ATTRIBUTES,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    RAC_TRANSFORM_QUERY_PROJECTION,
    TYPE_MAPPING,
    BIG_QUERY_SCHEMA_METADATA_KEY,
    YOUTUBE_BIG_QUERY_SCHEMA,
    FILE_UPLOAD_BIG_QUERY_SCHEMA,
)
from utils.common_utils import (
    fetch_collection_data,
    get_prefix,
    get_query_config,
    update_query_metadata,
)
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
    DataSourceId,
)
from validation import validate_schema_from_table_id


meta_container = MetaContainer()


def get_bigquery_schema(data_source_id: str):
    match data_source_id:
        case DataSourceId.YT_COMMENTS.value:
            return YOUTUBE_BIG_QUERY_SCHEMA
        case DataSourceId.FILE_UPLOAD.value:
            return FILE_UPLOAD_BIG_QUERY_SCHEMA
        case _:
            return {}


def complete_append_process(query_id, table_id, bigquery_schema):
    """
    Completes the append process by sending a diagnostic message for completion.

    Parameters:
    - query_id (str): The ID of the query for which the process is completed.
    - table_id (str): The ID of the BigQuery table to validate.
    - bigquery_schema (dict): The schema to validate against the BigQuery table.

    Returns:
    - None

    Exception Handling:
    - None
    """

    validate_schema_from_table_id(table_id, bigquery_schema)
    message = (
        f"Capabilities append script completed successfully for query_id: {query_id}"
    )
    meta_container.send_diagnostic(
        DiagnosticActionType.UPDATE.value,
        DiagnosticStatus.COMPLETED.value,
        message,
    )


def deduplicate_data(table_id: str):
    """
    Deduplicate a BigQuery table by removing duplicates based on the `_id` field.

    Parameters:
    - table_id (str): The BigQuery table ID in the format 'project_id.dataset_id.table_id'.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Split the table_id into project_id, dataset_id, and table_name
    project_id, dataset_id, table_name = table_id.split(".")
    # Query to get the table schema
    schema_query = f"SELECT * FROM `{project_id}.{dataset_id}.INFORMATION_SCHEMA.COLUMNS` WHERE table_name = '{table_name}'"
    # Fetch the table schema
    schema_df = read_gbq(schema_query)
    # Prepare the deduplication query dynamically based on the columns
    select_columns = []
    for column in schema_df["column_name"]:
        if column != "_id":  # Skip the _id column, as it will be in GROUP BY
            select_columns.append(
                f"(ARRAY_AGG({column} LIMIT 1))[OFFSET(0)] AS {column}"
            )
    # Build the complete query string
    select_columns_str = ",\n  ".join(select_columns)
    deduplication_query = f"""
    CREATE OR REPLACE TABLE `{table_id}` AS
    SELECT
      _id,
      {select_columns_str}
    FROM
      `{table_id}`
    GROUP BY
      _id;
    """
    # Execute the deduplication query
    read_gbq(deduplication_query)
    logger.info("Table `%s` has been deduplicated successfully.", table_name)


def filter_duplicates(df: pd.DataFrame, table_id: str, query_id: str):
    """
    Checks if any '_id' values from the input DataFrame already exist in the BigQuery table
    and returns a filtered DataFrame without duplicates.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing the records.
    - table_id (str): The BigQuery table ID in the format 'project_id.dataset_id.table_id'.
    - query_id (str): The string representation of the query_id.

    Returns:
    - pd.DataFrame: The filtered DataFrame with non-duplicate records.

    Exception Handling:
    - 404 Not Found: Logs a warning and returns the original DataFrame if the table does not exist in BigQuery.
    - Exception: Raised if any error occurs while filtering duplicates.
    """
    try:
        # Extract IDs to check
        ids_to_check = df["_id"].tolist()
        ids_str = ",".join(map(repr, ids_to_check))
        # If there are no IDs to check, return the original DataFrame
        if not ids_to_check:
            return df

        # Build the query to find existing _id values in BigQuery
        query = f"""
        SELECT _id
        FROM `{table_id}`
        WHERE _id IN ({ids_str})
        """
        # Fetch the existing IDs from BigQuery
        result = read_gbq(query)
        existing_ids = set(result["_id"].astype(str))  # Convert IDs to string
        # Filter the DataFrame to remove duplicates
        filtered_df = df[~df["_id"].astype(str).isin(existing_ids)]
        # Log duplicate information if found
        duplicate_count = len(existing_ids)
        if duplicate_count > 0:
            logger.warning(
                f"Skipping {duplicate_count} duplicate ID(s) found in the '{table_id}' BigQuery table for query ID '{query_id}'."
            )

        return filtered_df

    except GenericGBQException as e:
        # Check if exception message contains 404 error information
        if "Not Found" in str(e) or "404" in str(e):
            logger.warning(
                f"BigQuery Table `{table_id}` does not exist for query ID '{query_id}', Returning original DataFrame."
            )
            return df  # Return original DataFrame since table doesn't exist
        raise Exception(f"Error while filtering duplicates: {e}") from e


def get_total_records(table_id: str):
    """
    Fetch the total number of records based on the '_id' parameter from a BigQuery table.

    Parameters:
    - table_id (str): The BigQuery table ID in the format `project_id.dataset_id.table_id`.

    Returns:
    - int: The total number of records based on '_id' in the specified BigQuery table.

    Exception Handling:
    - None
    """
    # Construct the SQL query to count the total number of records
    query = f"SELECT COUNT(*) as total_record_count FROM `{table_id}`"
    # Execute the query and fetch the result
    result = read_gbq(query)
    # Get the total number of records from the result DataFrame
    total_record_count = result["total_record_count"].iloc[0]

    return total_record_count


def typecast_columns(df, type_mapping):
    """
    Typecast multiple DataFrame columns to specified data types and convert unspecified columns to string type.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing the columns to typecast.
    - type_mapping (dict): A dictionary where keys are column names and values are target data types.

    Returns:
    - pd.DataFrame: The DataFrame with typecasted columns.

    Exception Handling:
    - None
    """
    # Mapping for pandas-specific nullable data types
    nullable_type_map = {
        "int64": pd.Int64Dtype(),
    }
    # Prepare a dictionary for final type mapping
    final_mapping = {
        col: nullable_type_map.get(target_type, target_type)
        for col, target_type in type_mapping.items()
        if col in df.columns
    }
    # Apply typecasting for columns specified in type_mapping
    df = df.astype(final_mapping)
    # Convert all columns to pd.StringDtype() if they are not in type_mapping
    cols_to_convert = [col for col in df.columns if col not in type_mapping]
    if cols_to_convert:
        df[cols_to_convert] = df[cols_to_convert].astype("string")

    return df


def insert_into_bigquery(df: pd.DataFrame, query_id: str, table_id: str, prefix: str):
    """
    Inserts a Pandas DataFrame into a specified BigQuery table.

    Parameters:
    - df (pandas.DataFrame): The DataFrame containing the data to be inserted into the BigQuery table.
    - query_id (str): The string representation of the query_id.
    - table_id (str): The ID of the BigQuery table where the data should be inserted, in the format `project_id.your_dataset.your_table`.

    Returns:
    - bool: True if the data insertion in collection was successful, False otherwise.

    Exception Handling:
    - Exception: If any error occurs during the insertion process, the error message is sent to the diagnostic engine.
    """
    try:
        # Define the BigQuery table schema for specific columns
        schema = []
        if f"{prefix}{DATE_UTC_COLUMN}" in df.columns:
            schema.append({"name": f"{prefix}{DATE_UTC_COLUMN}", "type": "DATE"})
        # Insert the DataFrame into the BigQuery table
        to_gbq(df, table_id, table_schema=schema, if_exists="append")
        logger.info(
            "Inserted '%d' rows into '%s' for query_id: '%s'.",
            len(df),
            table_id,
            query_id,
        )

        return True

    except Exception as e:
        message = f"An error occurred while inserting data for '{query_id}' query_id into '{table_id}' bigquery table: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return False


def update_offset_and_publish(
    offset,
    total_documents,
    loopback_threshold,
    payload,
    batch_count,
    query_collection,
    query_id,
):
    """
    Updates the offset based on the number of documents fetched,
    and publishes a Pub/Sub message if necessary.

    Parameters:
    - offset (int): The current offset.
    - total_documents (int): The total number of documents in collection.
    - loopback_threshold (int): Threshold for the loopback.
    - payload (dict): The data payload to be published.
    - batch_count (int): The number of fetched documents.
    - query_collection (pymongo.collection.Collection): The MongoDB query collection.
    - query_id (str) : query ID for the query

    Returns:
    - int: The new offset.

    Exception Handling:
    - None
    """
    # Calculate the new offset
    new_offset = offset + batch_count
    # Update the query metadata with the new offset
    update_query_metadata(
        query_collection,
        query_id,
        APPEND_OFFSET_METADATA_KEY,
        new_offset,
    )
    # Publish a Pub/Sub message if there are more documents to process
    if loopback_threshold <= new_offset < total_documents:
        publish_pubsub_message(PROJECT_ID, APPEND_TOPIC_ID, payload)
        logger.info(
            "Published loopback message for offset %d out of %d",
            new_offset,
            total_documents,
        )

    return new_offset


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered from a message on a Cloud Pub/Sub topic.

    - This function is triggered by a Cloud Pub/Sub message and performs data fetch from MongoDb RAC transform collection, process data,
    and then inserts into big query table based on the provided payload.

    Steps:
    - Decode the cloud event payload to extract relevant data.
    - Retrieve organization and query information from the payload.
    - Toggle diagnostic status to 'PENDING'.
    - Retrieve MongoDB connection details from the organization vault using the organization ID.
    - Fetch the query configuration from the MongoDB database.
    - Retrieve the RAC transform collection name from query metadata.
    - Process data in chunks while the offset is less than the loopback threshold:
        - Fetch query data in chunks using the provided offset and chunk size.
        - Typecast multiple DataFrame columns to specified data types and convert unspecified columns to string type.
        - Checks if any '_id' values from the input DataFrame already exist in the BigQuery table
          and returns a filtered DataFrame without duplicates.
        - Inserts the Pandas DataFrame into a specified BigQuery table.
        - If there are more documents to process (new_offset >= loopback_threshold), loop back with the new offset by sending pub sub message to append topic id.
        - Deduplicate BigQuery table by removing duplicates based on the `_id` field if duplicates exists.
        - If all documents are processed (new_offset equals total_documents),initiate the schema validation for the BigQuery table. If the validation passes, send a message to the sequence coordinator Pub/Sub topic. If the validation fails, raise an exception.
        - Toggle diagnostic status to 'COMPLETED'.
    - If an error occurs during the transformation, toggle diagnostic status to 'FAILED'.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - tuple: A success message and the corresponding HTTP status code.

    Exception Handling:
    - Exception: If any error occurs during the append process.
    """
    mongodb_client = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)
        payload["publisher_type"] = CONSUMER_TYPE
        data_source_id = payload["data_source_id"]
        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        is_deduplication_required = payload.get("append_loopback_checkpoint", {}).get(
            "is_deduplication_required", False
        )

        message = (
            f"Capabilities append script started successfully for query_id: {query_id}"
        )
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return
        # BIG_QUERY_SCHEMA = query_config["source"]["meta_data"].get(
        #     BIG_QUERY_SCHEMA_METADATA_KEY,{}
        # )
        BIG_QUERY_SCHEMA = get_bigquery_schema(data_source_id)
        if BIG_QUERY_SCHEMA == {}:
            logger.warning(
                f"Unknown or unhandled data_source_id: '{data_source_id}'. Defaulting to an empty schema."
            )
        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )
        offset = query_meta_data.get(APPEND_OFFSET_METADATA_KEY, 0)
        rac_transform_collection_name = query_meta_data.get(
            RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
        )
        rac_transform_collection = get_mongodb_collection(
            organization_db, rac_transform_collection_name
        )
        table_id = (
            f"{PROJECT_ID}.{organization_db_name}.{rac_transform_collection_name}"
        )
        # Get bigquery table schema
        bigquery_schema = BIG_QUERY_SCHEMA

        prefix = get_prefix(data_source_id)

        # Deduplicate data if required
        if is_deduplication_required:
            deduplicate_data(table_id)
            payload.pop("append_loopback_checkpoint", None)
            publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
            complete_append_process(query_id, table_id, bigquery_schema)
            return "Success", 200

        # Retrieve the RAC transform collection volume
        total_documents = query_meta_data.get(
            RAC_TRANSFORM_VOLUME_METADATA_KEY
        ) or rac_transform_collection.count_documents({}, hint="_id_")

        loopback_threshold = offset + int(BASE_LOOPBACK_THRESHOLD)
        while offset < loopback_threshold:
            rac_df = fetch_collection_data(
                rac_transform_collection,
                {},
                RAC_TRANSFORM_QUERY_PROJECTION,
                offset,
                int(CHUNK_SIZE),
            )
            batch_count = len(rac_df)
            if not rac_df.empty:
                rac_df = typecast_columns(rac_df, TYPE_MAPPING)
                rac_df = filter_duplicates(rac_df, table_id, query_id)
            if not rac_df.empty:
                success = insert_into_bigquery(rac_df, query_id, table_id, prefix)
                if success:
                    offset = update_offset_and_publish(
                        offset,
                        total_documents,
                        loopback_threshold,
                        payload,
                        batch_count,
                        query_collection,
                        query_id,
                    )
                    bigquery_record_count = get_total_records(table_id)
                    # Ideal scenario: No duplicate records present
                    if offset == total_documents == bigquery_record_count:
                        publish_pubsub_message(
                            PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                        )
                        complete_append_process(query_id, table_id, bigquery_schema)
                        break
                    # Handling scenario where duplicate data exists in BigQuery
                    if (
                        offset >= total_documents
                        and bigquery_record_count > total_documents
                    ):
                        # Update the payload with the deduplication flag
                        payload.setdefault("append_loopback_checkpoint", {})[
                            "is_deduplication_required"
                        ] = True
                        publish_pubsub_message(PROJECT_ID, APPEND_TOPIC_ID, payload)
                        break
                else:
                    break
            else:
                if total_documents == get_total_records(table_id):
                    # Update the query metadata with the new offset
                    update_query_metadata(
                        query_collection,
                        query_id,
                        APPEND_OFFSET_METADATA_KEY,
                        total_documents,
                    )
                    complete_append_process(query_id, table_id, bigquery_schema)
                    break

                # Handling the case where all records in the batch are duplicates
                if batch_count:
                    offset = update_offset_and_publish(
                        offset,
                        total_documents,
                        loopback_threshold,
                        payload,
                        batch_count,
                        query_collection,
                        query_id,
                    )
                else:
                    break

        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities append: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
    finally:
        if mongodb_client is not None:
            mongodb_client.close()