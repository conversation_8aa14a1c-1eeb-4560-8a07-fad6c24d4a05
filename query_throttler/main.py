"""
This module creates a query throttler to process queries according
to the resources in a controlled way.
"""

import copy
import time
import json
import base64
from bson import ObjectId
from cloudevents.http import CloudEvent
import functions_framework
import numpy as np
from utils.common_utils import get_query_config
from kubernetes import client
from google.auth import default
from google.auth.transport.requests import Request
from google.cloud import container_v1
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_db,
    get_mongodb_collection,
)
from utils.utilities import (
    DataSourceId,
    OrganizationAccountInfo,
    MetaContainer,
    PublisherType,
    QueryStatus,
)
from const import (
    BASE_POD_MANIFEST,
    CAPABILITIES_DB_NAME,
    CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME,
    CAPABILITIES_MONGO_URI,
    CONSUMER_TYPE,
    POD_SIZING_CONFIG_COLLECTION_NAME,
    PROJECT_ID,
    QUERY_PROJECTION_ATTRIBUTES,
    QUERY_THROTTLER_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    NAMESPACE,
    DEFAULT_POD_NAME,
    MAX_RUNNING_QUERIES,
    QUERY_COLLECTION_NAME,
    REGION,
    CLUSTER_NAME,
    YT_COMMENT_SIZE_PER_RECORD_GB,
)

gke_client = container_v1.ClusterManagerClient()


def get_gke_cluster_credentials(project_id, region, cluster_name):
    """
    Get GKE cluster credentials.
    """
    # Get the cluster details
    cluster = gke_client.get_cluster(
        name=f"projects/{project_id}/locations/{region}/clusters/{cluster_name}"
    )

    # Extract the endpoint and the authentication information
    endpoint = cluster.endpoint

    # Get default credentials and refresh the token
    credentials, _ = default()
    credentials.refresh(Request())
    token = credentials.token

    return endpoint, token


def create_k8s_client(endpoint, token):
    """
    Create a Kubernetes client using the provided endpoint and token.
    """
    configuration = client.Configuration()
    configuration.host = f"https://{endpoint}"
    configuration.verify_ssl = False  # Set to True in production
    configuration.api_key["authorization"] = (
        f"Bearer {token}"  # Keep this if using token-based auth
    )

    return client.CoreV1Api(client.ApiClient(configuration))


def fetch_ip(api, pod_name):
    """
    Wait for the pod to be in a 'Running' state and return its IP address.
    """
    while True:
        pod = api.read_namespaced_pod(name=pod_name, namespace=NAMESPACE)

        if pod.status.phase == "Running":
            return pod.status.pod_ip

        elif pod.status.phase == "Failed":
            logger.info(f"Pod '{pod_name}' creation is failed.")
            return None

        logger.info(f"Waiting for pod '{pod_name}' to be ready...")
        time.sleep(5)  # Wait before checking again


def generate_pod_manifest(template: dict, pod_name: str, pod_config: dict) -> dict:
    """
    Generate a Kubernetes Pod manifest by replacing placeholders in a template.

    Parameters:
    - template (dict): A dictionary representing the base Pod manifest with
                        placeholder strings (e.g., "__POD_NAME__") for dynamic values.
    - pod_name (str): Name of the pod to be created.
    - pod_config (dict): Dictionary containing pod resource configuration.

    Returns:
    - dict: A new Pod manifest dictionary with all placeholders replaced by the corresponding values.

    Exception Handling:
    - None
    """
    replacements = {
        "POD_NAME": pod_name,
        "NAMESPACE": NAMESPACE,
        "APP_NAME": "vm-consumers",
        "CONTAINER_NAME": "vm-consumers",
        "IMAGE": f"gcr.io/{PROJECT_ID}/vm-consumers:latest",
        "MEMORY_REQUEST": pod_config["resources"]["requests"]["memory"],
        "CPU_REQUEST": pod_config["resources"]["requests"]["cpu"],
        "MEMORY_LIMIT": pod_config["resources"]["limits"]["memory"],
        "CPU_LIMIT": pod_config["resources"]["limits"]["cpu"],
    }

    pod_manifest = copy.deepcopy(template)
    json_str = json.dumps(pod_manifest)

    for key, value in replacements.items():
        json_str = json_str.replace(f"__{key}__", value)

    return json.loads(json_str)


def create_pod_from_manifest(pod_manifest: dict, pod_name: str, api):
    """
    Function to create POD for each query.

    Parameters:
    - pod_manifest (dict): A dictionary representing the base Kubernetes Pod manifest.
    - pod_name (str): Name of POD to be created.
    - api: Kubernetes CoreV1Api client instance.

    Returns:
    - str | None: The IP address of the created pod if successful, otherwise None.

    Exception Handling:
    - None
    """
    try:
        api.create_namespaced_pod(namespace=NAMESPACE, body=pod_manifest)

        pod_ip = fetch_ip(api, pod_name)

        if pod_ip:
            logger.info(f"Pod '{pod_name}' created with IP: {pod_ip}")
            return pod_ip
        else:
            logger.info(f"Pod '{pod_name}' failed to start, using default POD.")
            return fetch_ip(api, DEFAULT_POD_NAME)

    except Exception as e:
        logger.info(f"Error creating pod: {e}")
        return None


def can_start_query(organization_id, query_throttler_collection):
    """
    Check if there is an available slot for the given organization's query.
    """
    running_count = query_throttler_collection.count_documents(
        {"organization_id": organization_id, "query_status": QueryStatus.PENDING.value}
    )
    return running_count < MAX_RUNNING_QUERIES


def delete_pod_by_ip(namespace, pod_ip, api):
    """
    Deletes a Kubernetes pod using its namespace and IP address without a for loop.

    Parameters:
    - namespace (str): The namespace of the pod.
    - pod_ip (str): The IP address of the pod.

    Returns:
    - str: Success or failure message.
    """

    try:
        # Check it was using Default POD
        default_pod_ip = fetch_ip(api, DEFAULT_POD_NAME)

        if pod_ip == default_pod_ip:
            logger.info("Default POD was being used. Terminating POD deletion step")
            return

        # Fetch all pods in the namespace
        pods = api.list_namespaced_pod(namespace)

        # Use list comprehension to filter the pod with the given IP
        target_pod = next(
            (pod.metadata.name for pod in pods.items if pod.status.pod_ip == pod_ip),
            None,
        )

        if not target_pod:
            return f"No pod found with IP {pod_ip} in namespace {namespace}."

        # Delete the identified pod
        api.delete_namespaced_pod(name=target_pod, namespace=namespace)
        logger.info(f"Pod {target_pod} with IP {pod_ip} deleted successfully.")

    except Exception as e:
        logger.exception(f"Error deleting pod: {e}")


def get_data_size_gb_by_source(
    data_source_id: str,
    meta_container: MetaContainer,
    rac_volume: int,
    yt_comment_size_per_record_gb: float,
) -> float:
    """
    Returns the estimated data size in GB based on the data source type.

    Parameters:
    - data_source_id (str): The data source identifier.
    - meta_container (MetaContainer): MetaContainer object containing meta_data.
    - rac_volume (int): The RAC collection volume.
    - yt_comment_size_per_record_gb (float): Size per YT comment record in GB.

    Returns:
    - float: Estimated data size in GB.
    """
    match data_source_id:
        case DataSourceId.FILE_UPLOAD.value:
            return meta_container.meta_data.get("ESTIMATED_DATA_SIZE_GB", 0)
        case DataSourceId.YT_COMMENTS.value:
            return rac_volume * float(yt_comment_size_per_record_gb)
        case _:
            logger.warning(f"Unknown data source type: {data_source_id}")
            return 0


def estimate_embedding_memory_gb(
    volume: int,
    dimension: int = 768,
    dtype: np.dtype = np.float32,
    buffer: float = 0.2,
    python_float_bytes: int = 24,
) -> float:
    """
    Estimate total RAM in GB (rounded to 2 decimal places) required to store and process embeddings.

    Includes:
    - Python list of lists (~24 bytes per float)
    - NumPy array (based on dtype, default float32)
    - Optional buffer (default: 20%)

    Parameters:
    - volume (int): Number of embeddings
    - dimension (int): Dimensions per embedding (default: 768)
    - dtype (np.dtype): NumPy dtype used in final array (default: np.float32)
    - buffer (float): Additional buffer (default: 0.2 for 20%)
    - python_float_bytes (int): Bytes per float in Python list (default: 24)

    Returns:
    - float: Total estimated memory in GB, rounded to 2 decimal places

    Exception Handling:
    - None
    """
    numpy_bytes = volume * dimension * np.dtype(dtype).itemsize
    list_bytes = volume * dimension * python_float_bytes
    total_bytes = (numpy_bytes + list_bytes) * (1 + buffer)
    total_gb = total_bytes / (1024**3)

    return round(total_gb, 2)


def calculate_total_memory_gb(
    embedding_size_gb: float, data_size_gb: float, buffer: float = 0.2
) -> float:
    """
    Calculates total memory required in GB including embeddings, data, and buffer.

    Parameters:
    - embedding_size_gb (float): Memory required for embeddings (NumPy array) in GB.
    - data_size_gb (float): Memory required for the rest of the data (excluding embeddings) in GB.
    - buffer (float): Multiplier to add as buffer. (e.g., 0.2 adds 20%, 2 doubles memory). Default is 0.2.

    Returns:
    - float: Total estimated memory in GB (rounded to 2 decimal places).
    """
    total_gb = embedding_size_gb + data_size_gb
    total_with_buffer = total_gb * (1 + buffer)
    return round(total_with_buffer, 2)


def get_pod_config_for_data_size(
    data_size_gb: float, pod_sizing_config_collection
) -> dict | None:
    """
    Returns the appropriate pod config based on the data size in GB.
    Always chooses the next higher tier for safety, except when already at the last tier.

    Parameters:
    - data_size_gb (float): Size of the data in GB.
    - pod_sizing_config_collection: MongoDB collection object containing the pod tier config.

    Returns:
    - dict | None: Selected config dictionary, or None if configs are unavailable.

    Exception Handling:
    - None
    """
    if data_size_gb is None:
        return None

    # Round for safer float comparison
    data_size_gb = round(data_size_gb, 2)

    # Fetch and sort tiers by min_data_size_gb
    tiers = list(pod_sizing_config_collection.find({}))
    if not tiers:
        return None

    # Safely filter valid configs with required keys
    valid_tiers = [
        tier
        for tier in tiers
        if "min_data_size_gb" in tier and "max_data_size_gb" in tier
    ]
    valid_tiers.sort(key=lambda x: x["min_data_size_gb"])

    for i, tier in enumerate(valid_tiers):
        min_gb = tier["min_data_size_gb"]
        max_gb = tier["max_data_size_gb"]

        if min_gb <= data_size_gb <= max_gb:
            # Return next higher tier if exists, else current
            return valid_tiers[i + 1] if i + 1 < len(valid_tiers) else tier

    # If no tier matched, return last tier as default
    return valid_tiers[-1] if valid_tiers else None


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered from a message on a Cloud Pub/Sub topic.

    - This function is triggered by a Cloud Pub/Sub message and coordinates query execution and resource management
      for organization-specific queries in a controlled way.

    Steps:
    - Decode the cloud event payload to extract relevant data.
    - Retrieve organization and query information from the payload.
    - Fetch MongoDB connection details for the organization using the organization ID.
    - Fetch the query configuration from the MongoDB database.
    - Retrieve the RAC collection volume and pod IP from query metadata.
    - Obtain GKE cluster credentials and create a Kubernetes client.
    - Check the current query status in the throttler collection.
    - If the query is NOT_STARTED and a slot is available:
        - Toggle query status to 'PENDING'.
        - Publish a message to the sequence coordinator Pub/Sub topic to start hydration.
    - If the query is PENDING and publisher type is 'cleansing' and pod is not yet created:
        - Estimate memory requirements for embeddings and data.
        - Select appropriate pod configuration based on total memory required.
        - Generate and create a Kubernetes pod for the query.
        - Store the pod IP in the query metadata.
        - Publish a message to the sequence coordinator Pub/Sub topic to start data encapsulation.
      - If pod already exists, log and skip pod creation.
    - If the query is COMPLETED or FAILED:
        - Delete the pod and throttler record for the query.
        - Check for any lined-up queries and trigger the next one if available.
    - Log and handle unexpected query statuses.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - None

    Exception Handling:
    - None
    """
    meta_container = MetaContainer()

    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )

        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        data_source_id = payload["data_source_id"]

        publisher_type = payload["publisher_type"]

        # Fetch organization account information
        org_account_info = OrganizationAccountInfo(organization_id)

        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)
        query_collection = get_mongodb_collection(organization_db, QUERY_COLLECTION_NAME)

        mongodb_client = get_mongodb_client(CAPABILITIES_MONGO_URI)
        capabilities_db = get_mongodb_db(mongodb_client, CAPABILITIES_DB_NAME)
        query_throttler_collection = get_mongodb_collection(
            capabilities_db, CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME
        )
        pod_sizing_config_collection = get_mongodb_collection(
            capabilities_db, POD_SIZING_CONFIG_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        # Set the meta data
        meta_container.set_meta_data(query_config["meta_data"])

        # Retrieve the pod IP
        pod_ip = meta_container.meta_data.get("POD_IP")

        # Get GKE cluster credentials
        endpoint, token = get_gke_cluster_credentials(PROJECT_ID, REGION, CLUSTER_NAME)

        # Create a Kubernetes client
        api = create_k8s_client(endpoint, token)

        current_query = query_throttler_collection.find_one(
            {"query_id": ObjectId(query_id)}
        )

        current_query_status = current_query["query_status"]

        match current_query_status:
            case QueryStatus.NOT_STARTED.value:
                if (
                    can_start_query(organization_id, query_throttler_collection)
                    and publisher_type == PublisherType.API_SERVER.value
                ):
                    # Mark the query as PENDING
                    query_throttler_collection.update_one(
                        {"query_id": ObjectId(query_id)},
                        {"$set": {"query_status": QueryStatus.PENDING.value}},
                    )
                    logger.info(
                        f"Starting query with id: {query_id} for organization with id: {organization_id}"
                    )
                    # Send message to sequence coordinator to get hydration started
                    payload["publisher_type"] = CONSUMER_TYPE
                    logger.info(
                        f"Sending message to sequence coordinator to start query with payload: {payload}"
                    )
                    publish_pubsub_message(
                        PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                    )
                else:
                    logger.info(
                        f"No available slots for organization with id: {organization_id}."
                    )

            case QueryStatus.PENDING.value:
                if publisher_type == PublisherType.CLEANSING.value and not pod_ip:
                    # Start the pod creation process
                    # Retrieve the RAC collection volume
                    rac_volume = meta_container.meta_data["RAC_VOLUME"]
                    # Estimate the memory required for embeddings based on RAC volume
                    estimate_embedding_memory = estimate_embedding_memory_gb(rac_volume)
                    # Calculate the data size in GB based on the data source type and metadata
                    data_size_gb = get_data_size_gb_by_source(
                        data_source_id,
                        meta_container,
                        rac_volume,
                        YT_COMMENT_SIZE_PER_RECORD_GB,
                    )
                    # Calculate the total memory required (embeddings + data + buffer)
                    total_memory_gb = calculate_total_memory_gb(
                        estimate_embedding_memory, data_size_gb
                    )
                    # Generate a unique pod name for this query
                    pod_name = f"pod-{query_id}"
                    # Select the appropriate pod configuration based on total memory required
                    pod_config = get_pod_config_for_data_size(
                        total_memory_gb, pod_sizing_config_collection
                    )
                    # Generate the Kubernetes pod manifest using the selected config and pod name
                    pod_manifest = generate_pod_manifest(
                        BASE_POD_MANIFEST, pod_name, pod_config
                    )
                    # Logic to create pod per query - and have a default POD
                    pod_ip = create_pod_from_manifest(pod_manifest, pod_name, api)
                    # Add POD IP in metadata of query collection
                    query_collection.update_one(
                        {"_id": ObjectId(query_id)}, {"$set": {"meta_data.POD_IP": pod_ip}}
                    )
                    # Send message to sequence coordinator to get data encapsulation started
                    payload["publisher_type"] = CONSUMER_TYPE
                    logger.info(
                        f"Sending message to sequence coordinator to start query with payload: {payload}"
                    )
                    publish_pubsub_message(
                        PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                    )
                else:
                    logger.warning(
                        "Pod with IP %s already exists. Skipping pod creation for query_id: %s",
                        pod_ip,
                        query_id,
                    )

            case QueryStatus.COMPLETED.value | QueryStatus.FAILED.value:
                # Delete the pod and record from collection
                logger.info(
                    f"Query {query_id} for organization {organization_id} is {current_query_status}. Deleting POD and record from throttler..."
                )
                query_throttler_collection.delete_one({"query_id": ObjectId(query_id)})
                # Logic to delete POD using ip upon completion of all vm consumers -- throttler will be called upon completion
                delete_pod_by_ip(NAMESPACE, pod_ip, api)

                logger.info(
                    f"Checking for lined up queries after completion of query {query_id}"
                )

                next_query = query_throttler_collection.find_one(
                    {"query_status": QueryStatus.NOT_STARTED.value}
                )

                if not next_query:
                    logger.info(f"No lined up queries found after query_id: {query_id}")
                    return

                next_payload = next_query.get("payload")
                logger.info(
                    f"Sending message to query throttler to start query with payload: {next_payload}"
                )
                publish_pubsub_message(PROJECT_ID, QUERY_THROTTLER_TOPIC_ID, next_payload)

            case _:
                logger.warning(
                    f"Unexpected query status '{current_query_status}' for query_id: {query_id}"
                )
    except Exception as e:
        logger.error(f"Error in query throttler: {e}")

    finally:
        if mongodb_client is not None:
            mongodb_client.close()