import os

MAX_RUNNING_QUERIES = 25  # Maximum queries allowed per organization
RETRY_DELAY = 15 * 60  # 15 minutes

PROJECT_ID = os.getenv("PROJECT_ID")
CONSUMER_TYPE = os.getenv("CONSUMER_TYPE")  # QUERY_THROTTLER
QUERY_THROTTLER_TOPIC_ID = os.getenv("QUERY_THROTTLER_TOPIC_ID")
QUERY_PROJECTION_ATTRIBUTES = {
    "name": 1,
    "meta_data": 1,
    "source.data_source_id": 1,
    "user_id": 1,
    "RAC_COLLECTION_NAME": 1,
}

CAPABILITIES_MONGO_URI = os.getenv("CAPABILITIES_MONGO_URI")
CAPABILITIES_DB_NAME = os.getenv("CAPABILITIES_DB_NAME")
CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME = os.getenv(
    "CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME"
)
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
NAMESPACE = os.getenv("NAMESPACE")
DEFAULT_POD_NAME = os.getenv("DEFAULT_POD_NAME")  # default-pod
YT_COMMENT_SIZE_PER_RECORD_GB = os.getenv("YT_COMMENT_SIZE_PER_RECORD_GB")
POD_SIZING_CONFIG_COLLECTION_NAME = os.getenv("POD_SIZING_CONFIG_COLLECTION_NAME")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
POD_DEFINITION_YAML = "pod-definition.yaml"

# Define your project ID, zone, and cluster name
REGION = os.getenv("REGION")
CLUSTER_NAME = os.getenv("CLUSTER_NAME")

# POD manifest
BASE_POD_MANIFEST = {
    "apiVersion": "v1",
    "kind": "Pod",
    "metadata": {
        "name": "__POD_NAME__",
        "namespace": "__NAMESPACE__",
        "labels": {"app": "__APP_NAME__"},
    },
    "spec": {
        "containers": [
            {
                "name": "__CONTAINER_NAME__",
                "image": "__IMAGE__",
                "ports": [{"containerPort": 8080}],
                "resources": {
                    "requests": {
                        "memory": "__MEMORY_REQUEST__",
                        "cpu": "__CPU_REQUEST__",
                    },
                    "limits": {
                        "memory": "__MEMORY_LIMIT__",
                        "cpu": "__CPU_LIMIT__",
                    },
                },
            }
        ],
        "restartPolicy": "Never",
    },
}
