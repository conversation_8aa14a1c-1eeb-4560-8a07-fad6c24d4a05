import os
QUERY_STATUS_METADATA_KEY = "STATUS"
INDEX_STATUS_METADATA_KEY = "INDEX_STATUS"
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

QUERY_ARCHIVER_RAC_STATUS_METADATA_KEY = "QUERY_ARCHIVER_RAC_STATUS"
QUERY_ARCHIVER_RAC_TRANSFORM_STATUS_METADATA_KEY= "QUERY_ARCHIVER_RAC_TRANSFORM_STATUS"

QUERY_RETRIEVER_RAC_STATUS_METADATA_KEY = "QUERY_RETRIEVER_RAC_STATUS"
QUERY_RETRIEVER_RAC_TRANSFORM_STATUS_METADATA_KEY = "QUERY_RETRIEVER_RAC_TRANSFORM_STATUS"

QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
PROJECT_COLLECTION_NAME = os.getenv("PROJECT_COLLECTION_NAME")
PROJECT_ID = os.getenv("PROJECT_ID")
RAC_VOLUME_METADATA_KEY = "RAC_VOLUME"
RAC_QUERY_PROJECTION = {
    "embedding": 0,
    "is_embedded": 0,
    "POS": 0,
}
RAC_CHUNK_SIZE = os.getenv("RAC_CHUNK_SIZE")
RAC_TRASFORM_CHUNK_SIZE = os.getenv("RAC_TRASFORM_CHUNK_SIZE")
ARCHIVER_BASE_LOOPBACK_THRESHOLD = os.getenv(
    "ARCHIVER_BASE_LOOPBACK_THRESHOLD"
)

RETRIEVER_BASE_LOOPBACK_THRESHOLD = os.getenv(
    "RETRIEVER_BASE_LOOPBACK_THRESHOLD"
)

QUERY_PROJECTION_ATTRIBUTES = {
    "name": 1,
    "meta_data": 1,
    "source.data_source_id": 1,
    "user_id": 1,
    "RAC_COLLECTION_NAME": 1,
}
QUERY_ARCHIVER_RAC_OFFSET_METADATA_KEY = "ARCHIVER_RAC_OFFSET"
QUERY_ARCHIVER_RAC_TRANSFORM_OFFSET_METADATA_KEY = "ARCHIVER_RAC_TRANSFORM_OFFSET"

QUERY_RETRIEVER_RAC_OFFSET_METADATA_KEY = "RETRIEVER_RAC_OFFSET"
QUERY_RETRIEVER_RAC_TRANSFORM_OFFSET_METADATA_KEY = "RETRIEVER_RAC_TRANSFORM_OFFSET"

QUERY_OPERATION_TYPE_METADATA_KEY = "QUERY_OPERATION_TYPE"
ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)
CAPABILITIES_CONSUMER_LIST_COLLECTION = os.getenv(
    "CAPABILITIES_CONSUMER_LIST_COLLECTION"
)
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
RAC_VOLUME_METADATA_KEY = "RAC_VOLUME"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
RAC_TRANSFORM_VOLUME_METADATA_KEY = "RAC_TRANSFORM_VOLUME"
QUERY_ARCHIVER_RETRIEVER_TOPIC_ID = os.getenv(
    "QUERY_ARCHIVER_RETRIEVER_TOPIC_ID"
)
DISCOVER_TOPIC_ID = os.getenv("DISCOVER_TOPIC_ID")
