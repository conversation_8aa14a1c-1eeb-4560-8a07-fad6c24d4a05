"""
This module contains constants used in files of data transformer consumer.
"""

import os
from utils.utilities import (
    CategorizationProcessingField,
    SentimentProcessingField,
    SummaryProcessingField,
    RacTransform,
)

BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
DATA_TRANSFORMER_TOPIC_ID = os.getenv("DATA_TRANSFORMER_TOPIC_ID")
PROJECT_ID = os.getenv("PROJECT_ID")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
RAC_TRANSFORM_COLLECTION_SUFFIX = os.getenv("RAC_TRANSFORM_COLLECTION_SUFFIX")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

COLUMNS_TO_FLATTEN = [
    RacTransform.prefix_snippet.value,
    RacTransform.prefix_user_upload_info.value,
    RacTransform.prefix_video_info.value,
]
DATA_UNIFIER_COLUMNS = {
    RacTransform.Tellagence_Date.value,
    RacTransform.Tellagence_ID.value,
    RacTransform.Tellagence_Text.value,
}
DATA_TRANSFORMER_OFFSET_METADATA_KEY = "DATA_TRANSFORMER_OFFSET"
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
RAC_VOLUME_METADATA_KEY = "RAC_VOLUME"
RAC_TRANSFORM_VOLUME_METADATA_KEY = "RAC_TRANSFORM_VOLUME"
RAC_QUERY_PROJECTION = {
    "embedding": 0,
    "Categorization_Embedding": 0,
    "is_embedded": 0,
    "POS": 0,
    SentimentProcessingField.CLUSTER.value: 0,
    SentimentProcessingField.STORY.value: 0,
    SentimentProcessingField.THEME.value: 0,
    SummaryProcessingField.CLUSTER.value: 0,
    SummaryProcessingField.STORY.value: 0,
    SummaryProcessingField.THEME.value: 0,
    CategorizationProcessingField.THEME.value: 0,
}
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{DATA_TRANSFORMER_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{RAC_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_VOLUME_METADATA_KEY}": 1,
}
YT_COMMENT_COL_PREFIXES = [RacTransform.prefix_user_upload_info.value]
YT_COMMENTS_COLUMNS_TO_RETAIN = [
    RacTransform._id.value,
    RacTransform.Tellagence_ID.value,
    RacTransform.Tellagence_Date.value,
    RacTransform.Tellagence_Text.value,
    RacTransform.YT_etag.value,
    RacTransform.YT_kind.value,
    RacTransform.YT_record_type.value,
    RacTransform.YT_replies.value,
    RacTransform.YT_author.value,
    RacTransform.YT_author_channel_url.value,
    RacTransform.YT_author_profile_image_url.value,
    RacTransform.YT_comment_id.value,
    RacTransform.YT_like_count.value,
    RacTransform.YT_reply_count.value,
    RacTransform.YT_video_id.value,
    RacTransform.BODY1.value,
    RacTransform.EMOJIS.value,
    RacTransform.EMOJIS_Unique.value,
    RacTransform.EMOJIS_Unique_Count.value,
    RacTransform.Hashtag.value,
    RacTransform.Hashtag_Position.value,
    RacTransform.Hashtag_Unique.value,
    RacTransform.Hashtag_Unique_Count.value,
    RacTransform.Keyword.value,
    RacTransform.Lemitized.value,
    RacTransform.Phrase.value,
    RacTransform.encapsulation_marker.value,
    RacTransform.cluster.value,
    RacTransform.Stories.value,
    RacTransform.Themes.value,
    RacTransform.Unique_Cluster_ID.value,
    RacTransform.Unique_Story_ID.value,
    RacTransform.cluster_summary.value,
    RacTransform.story_summary.value,
    RacTransform.theme_summary.value,
    RacTransform.cluster_sentiment.value,
    RacTransform.cluster_sentiment_reasoning.value,
    RacTransform.snippet_channelId.value,
    RacTransform.snippet_videoId.value,
    RacTransform.snippet_canReply.value,
    RacTransform.snippet_totalReplyCount.value,
    RacTransform.snippet_isPublic.value,
    RacTransform.snippet_topLevelComment_kind.value,
    RacTransform.snippet_topLevelComment_etag.value,
    RacTransform.snippet_topLevelComment_id.value,
    RacTransform.snippet_topLevelComment_snippet_channelId.value,
    RacTransform.snippet_topLevelComment_snippet_videoId.value,
    RacTransform.snippet_topLevelComment_snippet_textDisplay.value,
    RacTransform.snippet_topLevelComment_snippet_textOriginal.value,
    RacTransform.snippet_topLevelComment_snippet_authorDisplayName.value,
    RacTransform.snippet_topLevelComment_snippet_authorProfileImageUrl.value,
    RacTransform.snippet_topLevelComment_snippet_authorChannelUrl.value,
    RacTransform.snippet_topLevelComment_snippet_authorChannelId_value.value,
    RacTransform.snippet_topLevelComment_snippet_canRate.value,
    RacTransform.snippet_topLevelComment_snippet_viewerRating.value,
    RacTransform.snippet_topLevelComment_snippet_likeCount.value,
    RacTransform.snippet_topLevelComment_snippet_publishedAt.value,
    RacTransform.snippet_topLevelComment_snippet_updatedAt.value,
    RacTransform.snippet_textDisplay.value,
    RacTransform.snippet_textOriginal.value,
    RacTransform.snippet_parentId.value,
    RacTransform.snippet_authorDisplayName.value,
    RacTransform.snippet_authorProfileImageUrl.value,
    RacTransform.snippet_authorChannelUrl.value,
    RacTransform.snippet_canRate.value,
    RacTransform.snippet_viewerRating.value,
    RacTransform.snippet_likeCount.value,
    RacTransform.snippet_publishedAt.value,
    RacTransform.snippet_updatedAt.value,
    RacTransform.snippet_authorChannelId_value.value,
    RacTransform.video_info_snippet_publishedAt.value,
    RacTransform.video_info_snippet_channelId.value,
    RacTransform.video_info_snippet_title.value,
    RacTransform.video_info_snippet_description.value,
    RacTransform.video_info_snippet_thumbnails_default_url.value,
    RacTransform.video_info_snippet_thumbnails_default_width.value,
    RacTransform.video_info_snippet_thumbnails_default_height.value,
    RacTransform.video_info_snippet_channelTitle.value,
    RacTransform.video_info_snippet_tags.value,
    RacTransform.video_info_snippet_categoryId.value,
    RacTransform.video_info_snippet_liveBroadcastContent.value,
    RacTransform.video_info_snippet_defaultLanguage.value,
    RacTransform.video_info_snippet_defaultAudioLanguage.value,
    RacTransform.video_info_contentDetails_duration.value,
    RacTransform.video_info_contentDetails_dimension.value,
    RacTransform.video_info_contentDetails_definition.value,
    RacTransform.video_info_contentDetails_caption.value,
    RacTransform.video_info_statistics_viewCount.value,
    RacTransform.video_info_statistics_likeCount.value,
    RacTransform.video_info_statistics_favoriteCount.value,
    RacTransform.video_info_statistics_commentCount.value,
    f"YT_{RacTransform.date_utc_str.value}",
    f"YT_{RacTransform.date_utc.value}",
    RacTransform.video_info_contentDetails_contentRating.value,
    RacTransform.video_info_contentDetails_regionRestriction_blocked.value,
    RacTransform.video_info_contentDetails_licensedContent.value,
    f"YT_{RacTransform.id.value}",
    RacTransform.theme_categorization_topic.value,
    RacTransform.theme_categorization_sub_topics.value,
    RacTransform.theme_categorization_explanation.value,
    RacTransform.theme_categorization_combined_topic.value,
    RacTransform.theme_categorization_cluster_name.value,
    RacTransform.theme_categorization_cluster_prediction.value,
    RacTransform.theme_categorization_notable_entities.value,
    RacTransform.theme_categorization_highlevel_categories.value,
    RacTransform.theme_categorization_subject.value,
    RacTransform.theme_categorization_object.value,
    RacTransform.Categorization_Client_Category_Primary.value,
    RacTransform.Categorization_Client_Category_Secondary.value,
    RacTransform.Categorization_Client_Category_Tertiary.value,
    RacTransform.Categorization_Client_Category_Primary_Explanation.value,
    RacTransform.Categorization_Client_Category_Secondary_Explanation.value,
    RacTransform.Categorization_Client_Category_Tertiary_Explanation.value,
]

CUSTOM_GENERATED_FIELD_MAPPING = {
    "_id": RacTransform._id.value,
    RacTransform.Tellagence_ID.value: RacTransform.Tellagence_ID.value,
    RacTransform.Tellagence_Date.value: RacTransform.Tellagence_Date.value,
    RacTransform.Tellagence_Text.value: RacTransform.Tellagence_Text.value,
    "BODY1": RacTransform.BODY1.value,
    "EMOJIS": RacTransform.EMOJIS.value,
    "EMOJIS_Unique": RacTransform.EMOJIS_Unique.value,
    "EMOJIS_Unique_Count": RacTransform.EMOJIS_Unique_Count.value,
    "Hashtag": RacTransform.Hashtag.value,
    "Hashtag_Position": RacTransform.Hashtag_Position.value,
    "Hashtag_Unique": RacTransform.Hashtag_Unique.value,
    "Hashtag_Unique_Count": RacTransform.Hashtag_Unique_Count.value,
    "Keyword": RacTransform.Keyword.value,
    "Lemitized": RacTransform.Lemitized.value,
    "Phrase": RacTransform.Phrase.value,
    "encapsulation_marker": RacTransform.encapsulation_marker.value,
    "cluster_id": RacTransform.cluster.value,
    "Stories": RacTransform.Stories.value,
    "Themes": RacTransform.Themes.value,
    "Unique_Cluster_ID": RacTransform.Unique_Cluster_ID.value,
    "Unique_Story_ID": RacTransform.Unique_Story_ID.value,
    "cluster_summary": RacTransform.cluster_summary.value,
    "story_summary": RacTransform.story_summary.value,
    "theme_summary": RacTransform.theme_summary.value,
    "cluster_sentiment": RacTransform.cluster_sentiment.value,
    "cluster_sentiment_reasoning": RacTransform.cluster_sentiment_reasoning.value,
}

CUSTOM_GENERATED_FIELDS = [
    RacTransform._id.value,
    RacTransform.Tellagence_ID.value,
    RacTransform.Tellagence_Date.value,
    RacTransform.Tellagence_Text.value,
    RacTransform.BODY1.value,
    RacTransform.EMOJIS.value,
    RacTransform.EMOJIS_Unique.value,
    RacTransform.EMOJIS_Unique_Count.value,
    RacTransform.Hashtag.value,
    RacTransform.Hashtag_Position.value,
    RacTransform.Hashtag_Unique.value,
    RacTransform.Hashtag_Unique_Count.value,
    RacTransform.Keyword.value,
    RacTransform.Lemitized.value,
    RacTransform.Phrase.value,
    RacTransform.encapsulation_marker.value,
    RacTransform.cluster.value,
    RacTransform.Stories.value,
    RacTransform.Themes.value,
    RacTransform.Unique_Cluster_ID.value,
    RacTransform.Unique_Story_ID.value,
    RacTransform.cluster_summary.value,
    RacTransform.story_summary.value,
    RacTransform.theme_summary.value,
    RacTransform.cluster_sentiment.value,
    RacTransform.cluster_sentiment_reasoning.value,
    RacTransform.theme_categorization_topic.value,
    RacTransform.theme_categorization_sub_topics.value,
    RacTransform.theme_categorization_explanation.value,
    RacTransform.theme_categorization_combined_topic.value,
    RacTransform.theme_categorization_cluster_name.value,
    RacTransform.theme_categorization_cluster_prediction.value,
    RacTransform.theme_categorization_notable_entities.value,
    RacTransform.theme_categorization_highlevel_categories.value,
    RacTransform.theme_categorization_subject.value,
    RacTransform.theme_categorization_object.value,
    RacTransform.Categorization_Client_Category_Primary.value,
    RacTransform.Categorization_Client_Category_Secondary.value,
    RacTransform.Categorization_Client_Category_Tertiary.value,
    RacTransform.Categorization_Client_Category_Primary_Explanation.value,
    RacTransform.Categorization_Client_Category_Secondary_Explanation.value,
    RacTransform.Categorization_Client_Category_Tertiary_Explanation.value,
]
